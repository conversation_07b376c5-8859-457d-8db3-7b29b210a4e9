# Gi<PERSON>i pháp cho lỗi Chunk Splitting

## Vấn đề
Lỗi `Cannot access 'he' before initialization` x<PERSON><PERSON> ra khi sử dụng `manualChunks` do:
- Circular dependencies giữa các chunks
- Thứ tự load không đúng
- Dependencies bị tách ra khỏi nhau

## Gi<PERSON>i pháp đã thử (theo thứ tự từ phức tạp đến đơn giản)

### 1. <PERSON><PERSON><PERSON> hì<PERSON> hiệ<PERSON> t<PERSON> (Conservative)
```javascript
manualChunks: {
    'primevue': ['primevue'],
    'lodash': ['lodash'], 
    'moment': ['moment'],
    'ably': ['ably'],
}
```

### 2. Nếu vẫn lỗi - Tắt hoàn toàn manualChunks
```javascript
// Xóa hoặc comment manualChunks
// manualChunks: { ... }
```

### 3. Giải pháp cuối cùng - Chỉ giữ tên file ngắn
Nếu vẫn có lỗi, sử dụng cấu hình này trong `vite.config.ts`:

```javascript
build: {
    chunkSizeWarningLimit: 1600, // Tăng limit thay vì split
    sourcemap: false,
    minify: 'esbuild',
    rollupOptions: {
        output: {
            entryFileNames: 'js/[name]-[hash:8].js',
            chunkFileNames: 'js/[name]-[hash:8].js',
            assetFileNames: (assetInfo) => {
                const fileName = assetInfo.names?.[0] || 'asset';
                const info = fileName.split('.');
                const ext = info[info.length - 1];
                if (/\.(css)$/.test(fileName)) {
                    return `css/[name]-[hash:8].${ext}`;
                }
                if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/i.test(fileName)) {
                    return `images/[name]-[hash:8].${ext}`;
                }
                return `assets/[name]-[hash:8].${ext}`;
            },
            // Không có manualChunks
        },
    },
}
```

## Cách test
1. Chạy `npm run build`
2. Chạy `npm run dev` hoặc serve production build
3. Kiểm tra console có lỗi không

## Lưu ý
- Chunk splitting có thể gây ra dependency issues
- Đôi khi tốt hơn là có 1 file lớn hơn là nhiều file nhỏ bị lỗi
- Tên file ngắn vẫn được giữ nguyên dù không split chunks
