#-------------------------------------------------------------------------------#
#               Qodana analysis is configured by qodana.yaml file               #
#             https://www.jetbrains.com/help/qodana/qodana-yaml.html            #
#-------------------------------------------------------------------------------#
version: "1.0"

#Specify the inspection profile for code analysis
profile:
  name: qodana.starter

#Enable inspections
#include:
#  - name: <SomeEnabledInspectionId>
include:
  - name: PhpComposerExtensionStubsInspection
  - name: PhpDocRedundantThrowsInspection
  - name: PhpUnhandledExceptionInspection
  - name: PhpUnnecessaryFullyQualifiedNameInspection
  #- name: PhpFullyQualifiedNameUsageInspection
  #- name: PhpMissingReturnTypeInspection

#Disable inspections
exclude:
  - name: All
    paths:
      - public/build

php:
  version: "8.3" #(Applied in CI/CD pipeline)

#Execute shell command before Qodana execution (Applied in CI/CD pipeline)
#bootstrap: sh ./prepare-qodana.sh

#Install IDE plugins before Qodana execution (Applied in CI/CD pipeline)
#plugins:
#  - id: <plugin.id> #(plugin id can be found at https://plugins.jetbrains.com)

#Specify Qodana linter for analysis (Applied in CI/CD pipeline)
linter: jetbrains/qodana-php:2025.1
