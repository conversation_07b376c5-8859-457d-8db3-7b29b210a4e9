<?php

namespace Tests\Unit;

use App\Helpers\ApiResponse;
use Tests\TestCase;

class ApiResponseTest extends TestCase
{
    public function test_success_response_with_data()
    {
        $data = ['user' => ['id' => 1, 'name' => 'John']];
        $meta = ['data' => 'test'];
        $response = ApiResponse::success($data, $meta);

        $this->assertEquals(0, $response['error_code']);
        $this->assertNull($response['error_message']);
        $this->assertNull($response['errors']);
        $this->assertEquals($data, $response['data']);
        $this->assertEquals($meta, $response['meta']);
    }

    public function test_error_response()
    {
        $message = 'Validation failed';
        $errors = ['email' => ['The email field is required']];
        $meta = ['api_version' => 'v2'];
        $response = ApiResponse::error(422, $message, $errors, $meta);

        $this->assertEquals(422, $response['error_code']);
        $this->assertEquals($message, $response['error_message']);
        $this->assertEquals($errors, $response['errors']);
        $this->assertNull($response['data']);
        $this->assertEquals($meta, $response['meta']);
    }

    public function test_response_with_app_version_check()
    {
        // Mock request with old app version
        $this->get('/test', ['appver' => '0.9']);

        $response = ApiResponse::success(['data' => 'test']);

        $this->assertEquals(2, $response['update_app']);
    }

    public function test_response_with_current_app_version()
    {
        // Mock request with current app version
        $this->get('/test', ['appver' => '1.0']);

        $response = ApiResponse::success(['data' => 'test']);

        $this->assertEquals(0, $response['update_app']);
    }
}
