<?php

use App\Exceptions\Handlers\ApiOrValidationExceptionHandler;
use App\Exceptions\Handlers\InertiaExceptionHandler;
use App\Exceptions\Handlers\ThrottleExceptionHandler;
use App\Helpers\ChatWork;
use App\Http\Middleware\HandleConsoleAccess;
use App\Http\Middleware\HandleInertiaRequests;
use App\Http\Middleware\JwtSanctumMiddleware;
use App\Services\ExceptionHandlerService;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Middleware\AddLinkHeadersForPreloadedAssets;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->throttleWithRedis();

        $middleware->web(append: [
            HandleInertiaRequests::class,
            AddLinkHeadersForPreloadedAssets::class,
        ]);

        $middleware->alias([
            'auth.jwt' => JwtSanctumMiddleware::class,
            'console' => HandleConsoleAccess::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        /**
         * send debug report for all exceptions
         */
        $exceptions->report(function (Throwable $exception) use ($exceptions) {
            ChatWork::sendDebugReport(request(), $exception);
        });

        /**
         * handle response exceptions
         */
        $exceptions->respond(function (Response $response, Throwable $exception, Request $request) {
            $service = new ExceptionHandlerService([
                new ThrottleExceptionHandler(),
                new ApiOrValidationExceptionHandler(),
                new InertiaExceptionHandler(),
            ]);

            return $service->handle($exception, $request, $response);
        });
    })->create();
