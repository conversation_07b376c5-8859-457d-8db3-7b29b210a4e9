{"private": true, "type": "module", "scripts": {"build": "vite build", "build:ssr": "vite build && vite build --ssr", "dev": "vite --host=localhost", "format": "prettier --write resources/", "format:check": "prettier --check resources/", "lint": "eslint . --fix"}, "devDependencies": {"@eslint/js": "^9.19.0", "@laravel/echo-vue": "^2.1.7", "@tailwindcss/postcss": "^4.1.11", "@types/lodash": "^4.17.16", "@types/node": "^24.3.0", "@vue/eslint-config-typescript": "^14.3.0", "eslint": "^9.17.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-vue": "^10.4.0", "laravel-echo": "^2.1.7", "prettier": "^3.4.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.11", "pusher-js": "^8.4.0", "sass-embedded": "^1.88.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript-eslint": "^8.23.0", "vue-tsc": "^3.0.6"}, "dependencies": {"@ably/laravel-echo": "^1.0.6", "@inertiajs/vue3": "^2.1.2", "@tailwindcss/vite": "^4.1.1", "@types/tailwindcss": "^3.1.0", "@vitejs/plugin-vue": "^6.0.1", "@vueuse/core": "^13.7.0", "ably": "^2.10.1", "autoprefixer": "^10.4.21", "chokidar": "^4.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "concurrently": "^9.0.1", "izitoast": "^1.4.0", "laravel-vite-plugin": "^2.0.0", "lodash": "^4.17.21", "lucide": "^0.540.0", "lucide-vue-next": "^0.540.0", "dayjs": "^1.11.13", "pinia": "^3.0.2", "postcss": "^8.5.6", "primevue": "^4.3.4", "reka-ui": "^2.2.0", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "typescript": "^5.2.2", "vite": "^7.1.3", "vue": "^3.5.13", "vue-i18n": "^11.1.3", "vue-toast-notification": "^3.1.3", "ziggy-js": "^2.4.2"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.47.0", "@tailwindcss/oxide-linux-x64-gnu": "^4.0.1", "lightningcss-linux-x64-gnu": "^1.29.1"}}