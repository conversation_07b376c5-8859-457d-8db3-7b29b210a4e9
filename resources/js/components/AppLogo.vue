<script setup lang="ts">
import AppLogoIcon from '@/components/AppLogoIcon.vue';
import { usePage } from '@inertiajs/vue3';
import { type SharedData } from '@/types';

const page = usePage<SharedData>();
// eslint-disable-next-line @typescript-eslint/no-wrapper-object-types
const appName = page.props.name as String;
</script>

<template>
    <div class="sidebar-header-logo">
        <AppLogoIcon class="size-12 fill-current" />
    </div>

    <div class="sidebar-header-app-name">
        <span class="mb-0.5 truncate font-semibold leading-none" v-text="appName" />
    </div>
</template>
