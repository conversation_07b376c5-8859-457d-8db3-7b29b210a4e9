<script setup lang="ts">
import { useAppearance } from '@/composables/useAppearance';
import { Monitor, Moon, Sun } from 'lucide-vue-next';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const { appearance, updateAppearance } = useAppearance();

const tabs = [
    { value: 'light', Icon: Sun, label: t('appearance.light') },
    { value: 'dark', Icon: Moon, label: t('appearance.dark') },
    { value: 'system', Icon: Monitor, label: t('appearance.system') },
] as const;
</script>

<template>
    <div class="inline-flex gap-2 rounded-lg bg-neutral-600/20 dark:bg-[var(--content-bg-color)] p-2">
        <button
            v-for="{ value, Icon, label } in tabs"
            :key="value"
            @click="updateAppearance(value)"
            :class="[
                'flex items-center rounded-md px-3.5 py-1.5 transition-colors hover:cursor-pointer',
                appearance === value
                    ? 'bg-white shadow-xs  dark:bg-zinc-950/50'
                    : 'text-neutral-700 hover:bg-neutral-100/60 hover:text-black dark:text-gray-300 dark:hover:bg-zinc-950/50',
            ]"
        >
            <component :is="Icon" class="-ml-1 h-4 w-4" />
            <span class="ml-1.5 text-sm">{{ label }}</span>
        </button>
    </div>
</template>
