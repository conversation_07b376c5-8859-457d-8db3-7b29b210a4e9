<script setup lang="ts">
import UserInfo from '@/components/UserInfo.vue';
import { DropdownMenuGroup, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import DialogWrapper from '@/components/ui/dialog/DialogWrapper.vue';
import type { User } from '@/types';
import { Link, router } from '@inertiajs/vue3';
import { LogOut, Settings } from 'lucide-vue-next';
import { useI18n } from 'vue-i18n';
import { ref } from 'vue';

const { t } = useI18n();

interface Props {
    user: User;
}

defineProps<Props>();

const loggingOut = ref(false);

const handleLogout = (e: Event) => {
    e.preventDefault();

    if (loggingOut.value) {
        return;
    }

    router.post(route('logout'), {}, {
        onStart: () => {
            loggingOut.value = true;
        },
        onSuccess: () => {
            loggingOut.value = false;
            router.flushAll();
        },
    });
};
</script>

<template>
    <DropdownMenuLabel class="p-0 font-normal">
        <div class="user-info-wrapper">
            <UserInfo :user="user" :show-email="true" />
        </div>
    </DropdownMenuLabel>
    <DropdownMenuSeparator />
    <DropdownMenuGroup>
        <DropdownMenuItem :as-child="true">
            <Link
                class="dropdown-menu-link"
                :href="route('profile.edit')" prefetch
                as="button"
            >
                <Settings class="mr-2 h-4 w-4" />
                {{ t('settings.label') }}
            </Link>
        </DropdownMenuItem>
    </DropdownMenuGroup>
    <DropdownMenuSeparator />
    <DropdownMenuItem :as-child="true">
        <DialogWrapper
            :busy="loggingOut"
            :action-callback="handleLogout"
            :action-label="t('logout.label')"
            action-data-type="dangerous"
        >
            <template #trigger>
                <button class="dropdown-menu-link gap-2 rounded-sm px-2 py-1.5 text-sm">
                    <LogOut class="mr-2 h-4 w-4" />
                    {{ t('logout.label') }}
                </button>
            </template>

            <template #title>{{ t('labels.confirmation') }}</template>
            <template #content>{{ t('logout.confirmationMessage') }}</template>
        </DialogWrapper>
    </DropdownMenuItem>
</template>
