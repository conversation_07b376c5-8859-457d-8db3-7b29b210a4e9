<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'
import { useVModel } from '@vueuse/core'

const props = withDefaults(
    defineProps<{
        defaultValue?: string | number
        modelValue?: string | number
        class?: HTMLAttributes['class']
        disabled?: boolean
    }>(), {
        disabled: false,
    }
)

const emits = defineEmits<{
  (e: 'update:modelValue', payload: string | number): void
}>()

const modelValue = useVModel(props, 'modelValue', emits, {
  passive: true,
  defaultValue: props.defaultValue,
})
</script>

<template>
  <input
    v-model="modelValue"
    data-slot="input"
    :class="cn(
      'input-field',
      props.class,
    )"
    autocomplete="off"
    :disabled="props.disabled"
  >
</template>
