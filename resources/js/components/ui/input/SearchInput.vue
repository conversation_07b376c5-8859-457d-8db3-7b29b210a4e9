<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'
import { useVModel } from '@vueuse/core'
import { X } from 'lucide-vue-next';
import Input from './Input.vue';

const props = defineProps<{
    defaultValue?: string | number
    modelValue?: string | number
    class?: HTMLAttributes['class']
    placeholder?: string
    onClearSearch: () => void
    onSearch: () => void
    disabled?: boolean
}>()

const emits = defineEmits<{
    (e: 'update:modelValue', payload: string | number): void
}>()

const modelValue = useVModel(props, 'modelValue', emits, {
    passive: true,
    defaultValue: props.defaultValue,
})
</script>

<template>
    <div :class="cn('relative w-64', props.class)">
        <Input
            id="search-keyword"
            type="text"
            class="w-full"
            :class="{ 'pr-8': modelValue }"
            v-model="modelValue"
            :placeholder="placeholder ?? ''"
            :disabled="disabled"
            @keydown.enter="onSearch"
        />

        <X
            v-if="modelValue && ! disabled"
            :size="16"
            class="absolute search-clear-icon hover:cursor-pointer hover:text-red-500 transition-colors"
            @click="onClearSearch"
        />
    </div>
</template>
