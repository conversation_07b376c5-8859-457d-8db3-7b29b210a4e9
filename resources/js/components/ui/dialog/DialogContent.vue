<script setup lang="ts">
import { cn } from '@/lib/utils'
import { computed, type HTMLAttributes, onMounted, onUnmounted } from 'vue'
import DialogOverlay from './DialogOverlay.vue'
import {
    DialogContent,
    type DialogContentEmits,
    type DialogContentProps,
    DialogPortal,
    useForwardPropsEmits,
} from 'reka-ui';

const props = defineProps<DialogContentProps & {
    class?: HTMLAttributes['class'],
    busy: boolean,
}>();

const emits = defineEmits<DialogContentEmits>()

const delegatedProps = computed(() => {
  const { class: _, ...delegated } = props

  return delegated
})

const forwarded = useForwardPropsEmits(delegatedProps, emits);

const preventOutsideClick = (e: any) => {
    if (props.busy) {
        e.preventDefault();
    }
};

const handleEscapeKey = (e: KeyboardEvent) => {
    if (e.key === 'Escape' && props.busy) {
        e.preventDefault();
        e.stopPropagation();
    }
};

onMounted(() => {
    document.addEventListener('keydown', handleEscapeKey);
});

onUnmounted(() => {
    document.removeEventListener('keydown', handleEscapeKey);
});
</script>

<template>
  <DialogPortal>
    <DialogOverlay />
    <DialogContent
      data-slot="dialog-content"
      v-bind="forwarded"
      :class="cn(
        'dialog-content data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out data-[state=open]:fade-in',
        props.class,
      )"
      @pointer-down-outside="preventOutsideClick"
      @escape-key-down="busy ? $event.preventDefault() : null"
    >
      <slot />
    </DialogContent>
  </DialogPortal>
</template>
