<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'
import { type ActionCallbackFunction } from '@/types';
import { DialogClose } from '@/components/ui/dialog/index';
import { LoaderCircle } from 'lucide-vue-next';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const props = defineProps<{
    class?: HTMLAttributes['class'],
    busy: boolean,
    actionCallback: ActionCallbackFunction,
    actionLabel: String,
    actionDataType: 'dangerous' | 'action',
}>()
</script>

<template>
  <div
    data-slot="dialog-footer"
    :class="cn('dialog-footer', props.class)"
  >
      <DialogClose as-child>
          <button type="button" class="dialog-btn-close" v-text="t('labels.cancel')" :disabled="busy" />
      </DialogClose>

      <div class="vertical-divider" />

      <button type="button" class="dialog-btn-action " :data-type="actionDataType" :disabled="busy" @click="actionCallback">
          <LoaderCircle v-if="busy" class="h-4 w-4 animate-spin mr-1" />
          {{ actionLabel }}
      </Button>
  </div>
</template>
