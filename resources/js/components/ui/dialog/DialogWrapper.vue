<script setup lang="ts">
import { type ActionCallbackFunction } from '@/types';
import {
    Dialog,
    DialogTitle,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTrigger
} from '@/components/ui/dialog/index';

defineProps<{
    busy: boolean,
    actionCallback: ActionCallbackFunction,
    actionLabel: String,
    actionDataType: 'dangerous' | 'action',
}>()
</script>

<template>
    <Dialog>
        <DialogTrigger as-child>
            <slot name="trigger" />
        </DialogTrigger>
        <DialogContent :busy="busy">
            <DialogHeader>
                <DialogTitle>
                    <slot name="title" />
                </DialogTitle>
            </DialogHeader>

            <DialogDescription class="grid gap-4">
                <slot name="content" />
            </DialogDescription>

            <DialogFooter
                :busy="busy"
                :action-callback="actionCallback"
                :action-label="actionLabel"
                action-data-type="dangerous"
            />
        </DialogContent>
    </Dialog>
</template>
