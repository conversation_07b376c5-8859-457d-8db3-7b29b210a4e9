<script setup lang="ts">
import { cn } from '@/lib/utils'
import { useEventListener, useVModel } from '@vueuse/core'
import { TooltipProvider } from 'reka-ui'
import { computed, type HTMLAttributes, type Ref } from 'vue'
import { provideSidebarContext, SIDEBAR_STATE_NAME, SIDEBAR_KEYBOARD_SHORTCUT } from './utils';

const props = withDefaults(defineProps<{
  defaultOpen?: boolean
  open?: boolean
  class?: HTMLAttributes['class']
}>(), {
  defaultOpen: true,
  open: undefined,
})

const emits = defineEmits<{
  'update:open': [open: boolean]
}>()

const open = useVModel(props, 'open', emits, {
  defaultValue: props.defaultOpen ?? false,
  passive: (props.open === undefined) as false,
}) as Ref<boolean>

function setOpen(value: boolean) {
  open.value = value; // emits('update:open', value)
  localStorage.setItem(SIDEBAR_STATE_NAME, open.value ? 'expanded' : 'collapsed');
}

// Helper to toggle the sidebar.
function toggleSidebar() {
  return setOpen(!open.value)
}

useEventListener('keydown', (event: KeyboardEvent) => {
  if (event.key === SIDEBAR_KEYBOARD_SHORTCUT && (event.metaKey || event.ctrlKey)) {
    event.preventDefault()
    toggleSidebar()
  }
})

// We add a state so that we can do data-state="expanded" or "collapsed".
// This makes it easier to style the sidebar with Tailwind classes.
const state = computed(() => open.value ? 'expanded' : 'collapsed')

provideSidebarContext({
  state,
  open,
  setOpen,
  toggleSidebar,
})
</script>

<template>
  <TooltipProvider :delay-duration="0">
    <div :class="cn('sidebar-wrapper', props.class)">
      <slot />
    </div>
  </TooltipProvider>
</template>
