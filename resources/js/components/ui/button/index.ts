import { cva, type VariantProps } from 'class-variance-authority'

export { default as <PERSON><PERSON> } from './Button.vue'

export const buttonVariants = cva(
  'btn-base',
  {
    variants: {
      variant: {
        primary:
          'btn-primary',
        destructive:
          'btn-destructive',
        outline:
          'btn-outline',
        secondary:
          'btn-secondary',
        ghost:
          'btn-ghost',
        link: 'button-link',
      },
      size: {
        default: 'btn-size-default',
        sm: 'btn-size-sm',
        lg: 'btn-size-lg',
        icon: 'size-9',
      },
    },
    defaultVariants: {
      variant: 'primary',
      size: 'default',
    },
  },
)

export type ButtonVariants = VariantProps<typeof buttonVariants>
