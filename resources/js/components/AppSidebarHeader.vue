<script setup lang="ts">
import { SidebarTrigger } from '@/components/ui/sidebar';
import type { ActionButton } from '@/types';
import { Button } from '@/components/ui/button';

withDefaults(defineProps<{
    right?: ActionButton,
    title: string,
}>(), {});
</script>

<template>
    <header class="app-header">
        <div class="flex items-center gap-2">
            <SidebarTrigger class="-ml-1" />
            <div v-text="title" class="flex-1 truncate font-semibold" />
        </div>

        <div class="ml-auto" v-if="right">
            <Button type="button" class="w-full hover:cursor-pointer text-sm" :size="right.size" :variant="right.variant" :disabled="right.disabled" @click="right.action()">
                {{ right.label }}
            </Button>
        </div>
    </header>
</template>
