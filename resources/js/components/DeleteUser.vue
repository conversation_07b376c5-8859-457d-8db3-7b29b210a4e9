<script setup lang="ts">
import { useForm } from '@inertiajs/vue3';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';

// Components
import HeadingSmall from '@/components/HeadingSmall.vue';
import InputError from '@/components/InputError.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import DialogWrapper from '@/components/ui/dialog/DialogWrapper.vue';

const { t } = useI18n();
const passwordInput = ref<HTMLInputElement | null>(null);

const form = useForm({
    password: '',
});

const deleteUser = (e: Event) => {
    e.preventDefault();

    form.delete(route('profile.destroy'), {
        preserveScroll: true,
        onSuccess: () => closeModal(),
        onFinish: () => form.reset(),
    });
};

const closeModal = () => {
    form.clearErrors();
    form.reset();
};
</script>

<template>
    <div class="space-y-6">
        <HeadingSmall :title="t('account.delete')" :description="t('account.deleteDescription')" />
        <div class="space-y-4 rounded-lg border border-red-200 bg-red-100 p-4 dark:border-red-200/10 dark:bg-red-700/10">
            <div class="relative space-y-0.5 text-gray-800 dark:text-gray-200">
                <p class="font-medium" v-text="t('labels.warning')" />
                <p class="text-sm" v-text="t('account.deleteWarning')" />
            </div>

            <DialogWrapper
                :busy="form.processing"
                :action-callback="deleteUser"
                :action-label="t('account.delete')"
                action-data-type="dangerous"
            >
                <template #trigger>
                    <Button variant="destructive">{{ t('account.deleteButton') }}</Button>
                </template>

                <template #title>{{ t('account.deleteConfirmTitle') }}</template>

                <template #content>
                    {{ t('account.deleteConfirmMessage') }}

                    <div class="grid">
                        <Label for="password" class="sr-only">{{ t('password.current') }}</Label>
                        <Input
                            id="password"
                            type="password"
                            name="password"
                            ref="passwordInput"
                            v-model="form.password"
                            :placeholder="t('password.current')"
                            :disabled="form.processing"
                        />
                        <InputError :message="form.errors.password" class="mt-1" />
                    </div>
                </template>
            </DialogWrapper>
        </div>
    </div>
</template>
