<template>
    <div class="grid-content-wrapper">
        <div class="grid-form-filter flex items-center">
            <slot name="filter" />
        </div>

        <Card
            class="rounded-lg p-0 shadow-none border-0 grid-data-table"
            :class="{ 'grid-loading': busy }"
        >
            <slot name="datatable" />

            <div class="absolute inset-0 flex items-center justify-center" v-if="busy">
                <LucideLoaderCircle class="h-8 w-8 animate-spin text-gray-600 dark:text-sky-600" />
            </div>
        </Card>

        <Pagination
            v-if="showPagination"
            class="flex items-center justify-center mx-auto"
            :links="links"
            :active="activePage"
            :disabled="busy"
            @progress="updateProgress"
        />
    </div>
</template>

<script setup lang="ts">
import { PaginationLink } from '@/types';
import Pagination from '@/components/Pagination.vue';
import { Card } from '@/components/ui/card';
import { ref } from 'vue';
import { LucideLoaderCircle } from 'lucide-vue-next';

const activePage = ref<number | null>(null);

defineProps<{
    busy: boolean,
    showPagination: boolean | undefined,
    links: PaginationLink[],
}>();

const emit = defineEmits<{
    (e: 'updateProgress', payload: number | null): void
}>()

const updateProgress = (progress: number | null) => {
    activePage.value = progress;
    emit('updateProgress', progress);
};
</script>
