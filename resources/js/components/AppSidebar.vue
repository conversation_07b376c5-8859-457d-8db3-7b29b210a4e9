<script setup lang="ts">
import NavMain from '@/components/NavMain.vue';
import NavUser from '@/components/NavUser.vue';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenuButton } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link } from '@inertiajs/vue3';
import { Users } from 'lucide-vue-next';
import AppLogo from './AppLogo.vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const mainNavItems: NavItem[] = [
    {
        title: t('user.label'),
        route: 'dashboard',
        icon: Users,
    },
];
</script>

<template>
    <Sidebar collapsible="icon" variant="inset">
        <SidebarHeader>
            <SidebarMenuButton size="lg" as-child class="app-logo">
                <Link :href="route('dashboard')">
                    <AppLogo />
                </Link>
            </SidebarMenuButton>
        </SidebarHeader>

        <SidebarContent>
            <NavMain :items="mainNavItems" />
        </SidebarContent>

        <SidebarFooter>
            <NavUser />
        </SidebarFooter>
    </Sidebar>
    <slot />
</template>
