<template>
    <div v-if="links.length > 3">
        <div class="flex flex-wrap -mb-1">
            <template v-for="(link, key) in links">
                <div
                    v-if="link.url === null"
                    :key="key"
                    class="pagination-item text-gray-400 dark:bg-[var(--content-bg-color)]"
                    :class="{ 'border rounded': link.label !== '...' }"
                    v-html="link.label"
                />

                <div
                    v-else-if="link.active"
                    :key="`active-${key}`"
                    class="pagination-item border rounded dark:text-gray-400 dark:bg-[var(--content-bg-color)]"
                    :class="{ 'bg-sky-500 dark:bg-sky-800 text-white font-semibold': !active, 'bg-gray-100/90': active }"
                    v-html="link.label"
                />

                <template v-else>
                    <div
                        v-if="disabled"
                        :key="`pagination-disabled-${key}`"
                        class="pagination-item border rounded dark:text-gray-400 dark:bg-[var(--content-bg-color)]"
                        :class="{ 'bg-sky-500 dark:bg-sky-800 text-white font-semibold': active === key, 'bg-gray-100/90': active !== key }"
                        v-html="link.label"
                    />

                    <InertiaLink
                        v-else
                        :key="`link-${key}`"
                        class="pagination-item bg-white hover:bg-sky-500 dark:hover:bg-sky-700 border rounded"
                        :href="link.url ?? ''"
                        :on-start="() => $emit('progress', key)"
                        :on-finish="() => $emit('progress', null)"
                    >
                        <span v-html="link.label" />
                    </InertiaLink>
                </template>
            </template>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Link as InertiaLink } from "@inertiajs/vue3";
import { PaginationLink } from '@/types';

defineProps<{
    links: PaginationLink[],
    active: number | null,
    disabled: boolean,
}>()

defineEmits<{
    (e: 'progress', payload: number | null): void
}>()
</script>
