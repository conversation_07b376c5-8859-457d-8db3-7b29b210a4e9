<script setup lang="ts">
import { SidebarProvider } from '@/components/ui/sidebar';
import { SIDEBAR_STATE_NAME } from '@/components/ui/sidebar/utils';

interface Props {
    variant?: 'header' | 'sidebar';
}

defineProps<Props>();

let isOpen = localStorage.getItem(SIDEBAR_STATE_NAME);
isOpen ??= 'expanded';
</script>

<template>
    <div v-if="variant === 'header'" class="flex min-h-screen w-full flex-col">
        <slot />
    </div>

    <SidebarProvider v-else :default-open="isOpen === 'expanded'">
        <slot />
    </SidebarProvider>
</template>
