import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs));
}

export function useIsEmptyForm<T extends Record<string, any>>(formData: () => T, defaults: T) {
  return () => {
    return Object.entries(formData()).every(([key, value]) => {
      const defaultVal = defaults[key as keyof T] ?? ''
      return value === '' || value === defaultVal
    })
  }
}
