import type { PageProps } from '@inertiajs/core';
import type { LucideIcon } from 'lucide-vue-next';
import type { Config } from 'ziggy-js';
import type { ButtonVariants } from '@/components/ui/button';

export interface Auth {
  user: User;
}

export interface NavItem {
  title: string;
  route: string;
  icon?: LucideIcon;
  isActive?: boolean;
}

export interface SharedData extends PageProps {
  name: string;
  auth: Auth;
  ziggy: Config & { location: string };
}

export interface User {
  user_id: number;
  name: string;
  email: string;
  avatar?: string;
  email_verified_at: string | null;
}

type ActionFunction = () => void;
export interface ActionButton {
  label: string;
  disabled?: boolean;
  variant?: ButtonVariants['variant'];
  size?: ButtonVariants['size'];
  action: ActionFunction;
}

export interface BaseFormFilter {
  keyword?: string,
}

export interface UserFormFilter extends BaseFormFilter {
  status?: number | string,
}

export interface PaginationLink {
  url?: string,
  label: string,
  active: boolean,
}

export interface UserData {
  user_id: number;
  name: string;
  email: string;
}

export interface PaginatorData {
  current_page: number,
  from: number,
  to: number,
  last_page: number,
  per_page: number,
  total: number,
  links: PaginationLink[],
}

export interface UserPaginatorData extends PaginatorData {
  data: UserData[],
}

export type ActionCallbackFunction = (e: Event) => void;
