<script setup lang="ts">
import { Head } from '@inertiajs/vue3';
import { useI18n } from 'vue-i18n';
import AppLayout from '@/layouts/app/AppLayout.vue';

const { t } = useI18n();

interface Props {
    message: string
}

defineProps<Props>();

const title = t('message.errorTitle');
</script>

<template>
    <Head :title="title" />

    <AppLayout :title="title">
        <div class="w-full h-full flex items-center justify-center">{{ message }}</div>
    </AppLayout>
</template>
