<script setup lang="ts">
import type { ActionButton, UserFormFilter, UserPaginatorData } from '@/types/index';
import { Head, useForm } from '@inertiajs/vue3';
import { useI18n } from 'vue-i18n';
import AppLayout from '@/layouts/app/AppLayout.vue';
import { SearchInput } from '@/components/ui/input/index';
import { computed, ref } from 'vue';
import PaginationPage from '@/components/PaginationPage.vue';
import { type SelectBoxOption, SelectBox } from '@/components/ui/selectbox';
import { useIsEmptyForm } from '@/lib/utils';
import { DataTable, Column } from 'primevue';

const { t } = useI18n();

const statusOptions: SelectBoxOption[] = [
    { value: 1, label: t('status.active') },
    { value: 0, label: t('status.deleted') },
];

const right: ActionButton = {
    label: t('user.create'),
    size: 'sm',
    action: () => {
        console.log('create user');
    },
};

interface Props {
    filters?: UserFormFilter
    paginator?: UserPaginatorData,
}

const props = withDefaults(defineProps<Props>(), {
    filters: () => ({
        keyword: '',
        status: '',
    }),
})

const form = useForm({
    keyword: props.filters['keyword'] ?? '',
    status: props.filters['status'] ?? '',
});

const isEmptyForm = useIsEmptyForm(() => form.data(), {
    keyword: '',
    status: '',
});

const search = () => {
    if (form.processing) {
        return false;
    }

    form.get(route('dashboard'), {
        preserveScroll: true,
        onSuccess: () => {},
    });
};

const clearSearch = () => {
    form.keyword = '';
    search();
}

const gridLoading = ref<boolean>(false);
const busy = computed(() => gridLoading.value || form.processing);

const updateProgress = (progress: number | null) => {
    gridLoading.value = progress !== null;
};

const title = t('user.list');
</script>

<template>
    <Head :title="title" />

    <AppLayout :title="title" :right="right">
        <PaginationPage
            :busy="busy"
            :show-pagination="props.paginator && props.paginator.data.length > 0"
            :links="props.paginator?.links ?? []"
            @updateProgress="updateProgress"
        >
            <template #filter>
                <div class="flex space-x-6">
                    <SearchInput
                        v-model="form.keyword"
                        :placeholder="t('labels.search')"
                        :on-search="search"
                        :on-clear-search="clearSearch"
                        :disabled="busy"
                    />

                    <SelectBox
                        class="w-56"
                        :options="statusOptions"
                        :placeholder="t('placeholder.selectStatus')"
                        :disabled="busy"
                        v-model="form.status"
                        @update:model-value="search"
                    />
                </div>
            </template>

            <template #datatable>
                <DataTable :value="props.paginator?.data" stripedRows>
                    <Column class="number-column" field="user_id" :header="t('fields.ID')" />
                    <Column class="flex-1" field="name" :header="t('user.name')" />
                    <Column class="w-[320px]" field="email" :header="t('email.label')" />
                    <Column class="action-column w-[100px]">

                    </Column>
                    <template #empty>{{ t(isEmptyForm() ? 'message.emptyData' : 'message.emptyResult') }}</template>
                </DataTable>
            </template>
        </PaginationPage>
    </AppLayout>
</template>
