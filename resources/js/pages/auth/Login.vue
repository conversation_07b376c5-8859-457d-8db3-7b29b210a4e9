<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import InputError from '@/components/InputError.vue';
import TextLink from '@/components/TextLink.vue';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AuthLayout from '@/layouts/auth/AuthLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { LoaderCircle } from 'lucide-vue-next';
import { useToast } from 'vue-toast-notification';

const toast = useToast();

defineProps<{
    status?: string;
    canResetPassword: boolean;
    canLoginWithGoogle: boolean;
}>();

const { t } = useI18n();

const form = useForm({
    email: '',
    password: '',
    remember: false,
    unauthorized: '',
});

const submit = () => {
    form.post(route('login'), {
        onFinish: () => form.reset('password'),
        onError: () => {
            if ('unauthorized' in form.errors) {
                toast.error(form.errors['unauthorized'] ?? '', {
                    position: 'top-right',
                });
            }
        }
    });
};

const redirectToGoogle = () => {
    window.location.href = route('google.redirect');
};
</script>

<template>
    <AuthLayout :title="t('login.title')" :description="t('login.description')">
        <Head :title="t('login.label')" />

        <div v-if="status" class="status-container">
            {{ status }}
        </div>

        <form @submit.prevent="submit" class="flex flex-col gap-6">
            <div class="grid gap-6">
                <div class="grid gap-2">
                    <Label for="email">{{ t('email.label') }}</Label>
                    <Input
                        type="email"
                        autofocus
                        :tabindex="1"
                        v-model="form.email"
                        placeholder="<EMAIL>"
                    />
                    <InputError :message="form.errors.email" />
                </div>

                <div class="grid gap-2">
                    <Label for="password">{{ t('password.label') }}</Label>
                    <Input
                        type="password"
                        :tabindex="2"
                        v-model="form.password"
                        :placeholder="t('password.label')"
                    />
                    <InputError :message="form.errors.password" />
                </div>

                <div class="flex items-center justify-between">
                    <Label for="remember" class="flex items-center space-x-2">
                        <Checkbox id="remember" v-model="form.remember" :tabindex="3" />
                        <span v-text="t('login.remember')" />
                    </Label>

                    <TextLink
                        v-if="canResetPassword"
                        :href="route('password.request')"
                        class="text-sm underline hover:text-sky-600"
                        :tabindex="5"
                    >{{ t('password.forgot') }}</TextLink>
                </div>

                <Button type="submit" class="w-full" :tabindex="4" :disabled="form.processing">
                    <LoaderCircle v-if="form.processing" class="h-4 w-4 animate-spin mr-1" />
                    {{ t('login.label') }}
                </Button>
            </div>
        </form>

        <div class="border-t border-gray-200 dark:border-[var(--sidebar-border-color)] pt-6" v-if="canLoginWithGoogle">
            <Button
                type="button"
                class="w-full"
                :tabindex="6"
                :disabled="form.processing"
                @click="redirectToGoogle"
            >{{ t('login.withGoogle') }}</Button>
        </div>
    </AuthLayout>
</template>
