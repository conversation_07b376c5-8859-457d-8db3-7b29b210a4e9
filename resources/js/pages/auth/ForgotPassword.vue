<script setup lang="ts">
import InputError from '@/components/InputError.vue';
import TextLink from '@/components/TextLink.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AuthLayout from '@/layouts/auth/AuthLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { LoaderCircle } from 'lucide-vue-next';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

defineProps<{
    status?: string;
}>();

const form = useForm({
    email: '',
});

const submit = () => {
    form.post(route('password.email'));
};
</script>

<template>
    <AuthLayout :title="t('password.forgot')" :description="t('password.forgotDescription')">
        <Head :title="t('password.forgot')" />

        <div v-if="status" class="status-container">
            {{ status }}
        </div>

        <div class="space-y-6">
            <form @submit.prevent="submit">
                <div class="grid gap-2">
                    <Label for="email">{{ t('email.address') }}</Label>
                    <Input id="email" type="email" name="email" v-model="form.email" autofocus placeholder="<EMAIL>" />
                    <InputError :message="form.errors.email" />
                </div>

                <div class="my-6 flex items-center justify-start">
                    <Button class="w-full" :disabled="form.processing">
                        <LoaderCircle v-if="form.processing" class="h-4 w-4 animate-spin" />
                        {{ t('password.resetButton') }}
                    </Button>
                </div>
            </form>

            <div class="text-center">
                <TextLink :href="route('login')" class="text-sm underline hover:text-sky-600">
                    <span v-text="t('or')" /> <span v-text="t('login.label').toLowerCase()" />
                </TextLink>
            </div>
        </div>
    </AuthLayout>
</template>
