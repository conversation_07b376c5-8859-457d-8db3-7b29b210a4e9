<script setup lang="ts">
import TextLink from '@/components/TextLink.vue';
import { Button } from '@/components/ui/button';
import AuthLayout from '@/layouts/auth/AuthLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { LoaderCircle } from 'lucide-vue-next';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

defineProps<{
    status?: string;
}>();

const form = useForm({});

const submit = () => {
    form.post(route('verification.send'));
};
</script>

<template>
    <AuthLayout :title="t('email.verify')" :description="t('email.verifyTip')">
        <Head :title="t('email.verify')" />

        <div
            v-if="status === 'verification-link-sent'"
            class="mb-4 text-center text-sm font-medium text-green-600"
            v-html="t('email.resendVerificationDone')"
        />

        <div class="space-y-6 text-center">
            <Button class="mx-auto dark:bg-sky-700 dark:hover:bg-sky-800 dark:text-gray-300" :disabled="form.processing" variant="secondary" @click="submit">
                <LoaderCircle v-if="form.processing" class="h-4 w-4 animate-spin" />
                {{ t('email.resendVerification') }}
            </Button>

            <TextLink :href="route('logout')" method="post" as="button" class="mx-auto block text-sm hover:cursor-pointer hover:underline">{{ t('logout.label') }}</TextLink>
        </div>
    </AuthLayout>
</template>
