<script setup lang="ts">
import AppContent from '@/components/AppContent.vue';
import AppShell from '@/components/AppShell.vue';
import AppSidebar from '@/components/AppSidebar.vue';
import AppSidebarHeader from '@/components/AppSidebarHeader.vue';
import type { ActionButton } from '@/types';
import { useEcho } from '@laravel/echo-vue';

interface Props {
    right?: ActionButton;
    title: string,
}

withDefaults(defineProps<Props>(), {
    title: '',
});

type MessageSent = {
    message: string;
};

useEcho<MessageSent>('chat.1', '.MessageSent', (e) => {
    console.log('Received:', e.message);
});
</script>

<template>
    <AppShell variant="sidebar">
        <AppSidebar />
        <AppContent variant="sidebar">
            <AppSidebarHeader :title="title" :right="right" />

            <div class="main-wrapper-content">
                <slot />
            </div>
        </AppContent>
    </AppShell>
</template>
