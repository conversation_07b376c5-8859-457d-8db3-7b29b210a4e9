<script setup lang="ts">
import Heading from '@/components/Heading.vue';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { type NavItem } from '@/types';
import { Link, usePage } from '@inertiajs/vue3';
import { useI18n } from 'vue-i18n';
import { Card } from '@/components/ui/card';

const { t } = useI18n();

const sidebarNavItems: NavItem[] = [
    {
        title: t('settings.profile'),
        route: '/settings/profile',
    },
    {
        title: t('password.change'),
        route: '/settings/password',
    },
    {
        title: t('settings.appearance'),
        route: '/settings/appearance',
    },
];

const page = usePage();

const currentPath = page.props.ziggy?.location ? new URL(page.props.ziggy.location).pathname : '';
</script>

<template>
    <Heading :title="t('settings.label')" :description="t('settings.description')" />

    <div class="flex space-x-6">
        <aside class="w-48">
            <nav class="flex flex-col space-x-0 space-y-1">
                <Button
                    v-for="item in sidebarNavItems"
                    :key="item.route"
                    variant="ghost"
                    :class="['setting-item', { 'active': currentPath === item.route }]"
                    as-child
                >
                    <Link :href="item.route">
                        {{ item.title }}
                    </Link>
                </Button>
            </nav>
        </aside>

        <Card class="flex-1 max-w-xl rounded-lg px-6 py-5">
            <section class="w-full space-y-6">
                <slot />
            </section>
        </Card>
    </div>
</template>
