<script setup lang="ts">
//import AppLogoIcon from '@/components/AppLogoIcon.vue';
//import { Link } from '@inertiajs/vue3';

defineProps<{
    title?: string;
    description?: string;
}>();
</script>

<template>
    <div class="flex min-h-svh flex-col items-center justify-center gap-6 p-6 md:p-10">
        <div class="w-full max-w-lg border border-gray-200 dark:border-[var(--sidebar-border-color)] rounded-md px-6 py-6 bg-white dark:bg-[var(--content-bg-color)] shadow-sm">
            <div class="flex flex-col gap-6">
                <div class="flex flex-col items-center gap-4">
                    <div class="space-y-2 text-center">
                        <h1 class="text-xl font-semibold">{{ title }}</h1>
                        <p class="text-center text-sm text-muted-foreground">{{ description }}</p>
                    </div>
                </div>

                <slot />
            </div>
        </div>
    </div>
</template>
