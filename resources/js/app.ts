import '../css/app.scss';
import 'vue-toast-notification/dist/theme-bootstrap.css';

import { createInertiaApp } from '@inertiajs/vue3';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import type { DefineComponent } from 'vue';
import { createApp, h } from 'vue';
import { ZiggyVue } from 'ziggy-js';
import { initializeTheme } from './composables/useAppearance';

import { createPinia } from 'pinia';
import i18n from './plugins/i18n';
import ToastPlugin from 'vue-toast-notification';
import { configureEcho } from '@laravel/echo-vue';

configureEcho({
  broadcaster: 'ably',
  key: import.meta.env.VITE_ABLY_PUBLIC_KEY,
  authEndpoint: '/broadcasting/auth',
});

const appName = import.meta.env.VITE_APP_NAME || 'Laravel';

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: (name) => resolvePageComponent(`./pages/${name}.vue`, import.meta.glob<DefineComponent>('./pages/**/*.vue')),
    setup({ el, App, props, plugin }) {
        createApp({ render: () => h(App, props) })
            .use(plugin)
            .use(createPinia())
            .use(ZiggyVue)
            .use(ToastPlugin)
            .use(i18n)
            .mount(el);
    },
    progress: {
      color: '#ea580c', //'#388E3C',
      showSpinner: false,
    },
}).then(() => console.log('Inertia app initialized!'));

// This will set light / dark mode on a page load...
initializeTheme();
