import { createI18n } from 'vue-i18n'
import vi from '../locales/vi.json'
import ja from '../locales/ja.json'

// <PERSON>h sách ngôn ngữ hỗ trợ
const supportedLocales = ['vi', 'ja'] as const;
type SupportedLocale = typeof supportedLocales[number];

// Lấy locale từ biến môi trường, fallback là 'vi'
const envLocale = import.meta.env.VITE_APP_LOCALE;
const locale: SupportedLocale = supportedLocales.includes(envLocale) ? envLocale as SupportedLocale : 'vi';

// Type-define 'vi' as master schema
type MessageSchema = typeof vi;

const i18n = createI18n<[MessageSchema], SupportedLocale>({
  legacy: false,
  locale,
  fallbackLocale: 'vi',
  messages: {
    vi,
    ja,
  }
});

export default i18n;
