<?php

namespace Database\Seeders;

use App\Enums\UserRole;
use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the app's database.
     */
    public function run(): void
    {
        User::factory()->create([
            'name' => 'DucTV',
            'email' => '<EMAIL>',
            'password' => bcrypt('123@123'),
            'role' => UserRole::ADMIN->value,
        ]);
    }
}
