<?php

namespace App\Services;

use App\Models\User;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use <PERSON>vel\Sanctum\PersonalAccessToken;
use Illuminate\Support\Facades\Auth;
use Throwable;

class JwtSanctumTokenService
{
    /**
     * @var string
     */
    protected string $secret = '';

    /**
     * @var string
     */
    protected string $algo = 'HS256';

    /**
     * Khởi tạo service với JWT secret từ config
     */
    public function __construct()
    {
        $this->secret = config('app.jwt_secret');
    }

    /**
     * Encode Sanctum access_token ID thành JWT
     */
    public function generateJwtFromSanctumToken(string $plainTextToken): string
    {
        $payload = [
            'planTextToken' => $plainTextToken,
        ];

        return JWT::encode($payload, $this->secret, $this->algo);
    }

    /**
     * Lấy Sanctum token từ JWT
     */
    public function getSanctumTokenFromJwt(string $jwt): ?PersonalAccessToken
    {
        try {
            $payload = JWT::decode($jwt, new Key($this->secret, $this->algo));
            $token = $payload->planTextToken ?? null;
            if (! $token) {
                return null;
            }

            return PersonalAccessToken::findToken($token);
        } catch (Throwable $e) {
            return null;
        }
    }

    /**
     * Xác thực user từ JWT
     */
    public function authenticateUserFromJwt(string $jwt): bool
    {
        $token = $this->getSanctumTokenFromJwt($jwt);

        if (! $token || ! ($user = $token->tokenable()->first())) {
            abort(401, 'Unauthorized');
        }

        /** @var User $user */
        $user->withAccessToken($token);

        Auth::setUser($user);

        return true;
    }
}
