<?php

namespace App\Services;

use App\Data\User\FetchUserDataDTO;
use App\Data\User\StoreUserDTO;
use App\Data\User\UpdateUserPasswordDTO;
use App\Events\UserUpdated;
use App\Models\User;
use App\Repositories\Contracts\UserRepositoryInterface;

class UserService
{
    public function __construct(protected UserRepositoryInterface $repository)
    {

    }

    /**
     * @param User $user
     * @param StoreUserDTO $dto
     * @return \Illuminate\Database\Eloquent\Model
     */
    public function updateUser(User $user, StoreUserDTO $dto)
    {
        $user = $this->repository->update($user, [
            'name' => $dto->getName(),
            'email' => $dto->getEmail(),
        ]);

        UserUpdated::dispatch($user);

        return $user;
    }

    /**
     * @param User $user
     * @param UpdateUserPasswordDTO $dto
     * @return void
     */
    public function updateUserPassword(User $user, UpdateUserPasswordDTO $dto)
    {
        $this->repository->update($user, [
            'password' => bcrypt($dto->getPassword()),
        ]);
    }

    /**
     * @param FetchUserDataDTO $dto
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function pagination(FetchUserDataDTO $dto)
    {
        return $this->repository->pagination($dto);
    }

    /**
     * @param array $ids
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function findByIds(array $ids)
    {
        return $this->repository->findMany($ids);
    }

    /**
     * @param User $user
     * @return void
     */
    public function delete(User $user)
    {
        $this->repository->destroy($user);
    }
}
