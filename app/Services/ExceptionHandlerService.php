<?php

namespace App\Services;

use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class ExceptionHandlerService
{
    /**
     * @param array $handlers
     */
    public function __construct(protected array $handlers)
    {

    }

    /**
     * @param Throwable $exception
     * @param Request $request
     * @param Response $response
     * @return Response
     */
    public function handle(Throwable $exception, Request $request, Response $response)
    {
        foreach ($this->handlers as $handler) {
            if ($handler->shouldHandle($exception, $request)) {
                return $handler->handle($exception, $request, $response->getStatusCode());
            }
        }

        return $response;
    }
}
