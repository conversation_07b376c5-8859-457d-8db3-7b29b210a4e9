<?php

namespace App\Events;

use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;

class MessageSent implements ShouldBroadcast
{
    use Dispatchable;

    /**
     * Create a new event instance.
     */
    public function __construct(protected string $message, protected int $roomId)
    {
        //
    }

    /**
     * @return PrivateChannel
     */
    public function broadcastOn()
    {
        return new PrivateChannel('chat.' . $this->roomId);
    }

    /**
     * @return string[]
     */
    public function broadcastWith(): array
    {
        return [
            'message' => $this->message,
        ];
    }

    /**
     * @return string
     */
    public function broadcastAs()
    {
        return 'MessageSent';
    }
}
