<?php

namespace App\Events\Domain;

use DateTimeInterface;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

abstract class BaseEvent
{
    use Dispatchable, SerializesModels;

    /**
     * @var string
     */
    public string $eventType;

    /**
     * @var DateTimeInterface|\Illuminate\Support\Carbon
     */
    public DateTimeInterface $occurredAt;

    /**
     * @return void
     */
    public function __construct()
    {
        $this->eventType = static::class;
        $this->occurredAt = now();
    }

    /**
     * Domain entity payload – override this in child class
     */
    abstract public function getEntity();
}
