<?php

namespace App\Data;

class CursorPaginationDTO
{
    public ?string $cursor;
    public int $limit;
    /** @var array<array{column: string, direction: string}> */
    public array $sorts;
    public array $filters;

    public function __construct(
        ?string $cursor = null,
        int $limit = 20,
        array $sorts = [['column' => 'id', 'direction' => 'desc']],
        array $filters = []
    ) {
        $this->cursor = $cursor;
        $this->limit = $limit;
        $this->sorts = $sorts;
        $this->filters = $filters;
    }
}
