<?php

namespace App\Data\User;

use App\Data\AbstractStoreDataDTO;

final class StoreUserDTO extends AbstractStoreDataDTO
{
    /**
     * @var string
     */
    protected string $name;

    /**
     * @var string
     */
    protected string $email;

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return string
     */
    public function getEmail(): string
    {
        return $this->email;
    }

    /**
     * @return array
     */
    public static function rules(): array
    {
        return [];
    }
}
