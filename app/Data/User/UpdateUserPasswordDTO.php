<?php

namespace App\Data\User;

use App\Data\AbstractStoreDataDTO;

class UpdateUserPasswordDTO extends AbstractStoreDataDTO
{
    /**
     * @var string
     */
    protected string $password;

    /**
     * @return string
     */
    public function getPassword(): string
    {
        return $this->password;
    }

    /**
     * @return array
     */
    public static function rules(): array
    {
        return [];
    }
}
