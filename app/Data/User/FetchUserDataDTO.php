<?php

namespace App\Data\User;

use App\Data\FetchDataDTO;

class FetchUserDataDTO extends FetchDataDTO
{
    protected ?int $status = null;

    /**
     * @param array $data 
     * @return void 
     */
    public function __construct(array $data)
    {
        parent::__construct($data);

        $this->setStatus($data['status'] ?? null);
    }

    /**
     * @param null|int $status 
     * @return $this 
     */
    public function setStatus(?int $status)
    {
        $this->status = $status;

        return $this;
    }

    /**
     * @return null|int 
     */
    public function getStatus(): ?int
    {
        return $this->status;
    }
}
