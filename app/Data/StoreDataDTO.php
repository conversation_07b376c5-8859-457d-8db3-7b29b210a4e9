<?php

namespace App\Data;

use App\Http\Requests\BaseRequest;

abstract class StoreDataDTO
{
    /**
     * @param array $attributes
     */
    public function __construct(protected array $attributes)
    {

    }

    /**
     * @param BaseRequest $request
     * @return static
     */
    public static function fromRequest(BaseRequest $request): static
    {
        return new static(static::map($request->validated()));
    }

    /**
     * @param array $validated
     * @return array
     */
    protected static function map(array $validated): array
    {
        return $validated;
    }

    /**
     * Get all attributes.
     */
    protected function all(): array
    {
        return $this->attributes;
    }

    /**
     * Get specific value with default.
     */
    protected function get(string $key, mixed $default = null): mixed
    {
        return $this->attributes[$key] ?? $default;
    }
}
