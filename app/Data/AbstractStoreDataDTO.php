<?php

namespace App\Data;

use App\Http\Requests\BaseRequest;
use Illuminate\Contracts\Support\Arrayable;
use ReflectionClass;
use ReflectionProperty;

/**
 * Class AbstractStoreDataDTO
 * Base class for Data Transfer Objects used for storing data.
 */
abstract class AbstractStoreDataDTO implements Arrayable
{
    /**
     * AbstractStoreDataDTO constructor.
     * @param array $attributes
     */
    public function __construct(array $attributes = [])
    {
        foreach ($this->getTypedProperties() as $property) {
            $name = $property->getName();

            if (! array_key_exists($name, $attributes)) {
                continue;
            }

            $value = $attributes[$name];

            if ($property->getType()?->getName() === 'bool') {
                $value = filter_var($value, FILTER_VALIDATE_BOOLEAN);
            }

            if ($property->getType()?->getName() === 'int') {
                $value = (int) $value;
            }

            if ($property->getType()?->getName() === 'float') {
                $value = (float) $value;
            }

            if ($property->getType()?->getName() === 'string') {
                $value = (string) $value;
            }

            $this->{$name} = $value;
        }
    }

    /**
     * Static factory from request
     */
    public static function fromRequest(BaseRequest $request): static
    {
        return new static($request->validated());
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        $props = $this->getTypedProperties();

        return collect($props)
            ->mapWithKeys(fn($p) => [$p->getName() => $this->{$p->getName()} ?? null])
            ->all();
    }

    /**
     * Get one field value (optional)
     */
    public function get(string $key, mixed $default = null): mixed
    {
        return $this->{$key} ?? $default;
    }

    /**
     * Get public typed properties
     */
    protected function getTypedProperties(): array
    {
        return (new ReflectionClass($this))
            ->getProperties(ReflectionProperty::IS_PROTECTED);
    }

    /**
     * @return array
     */
    abstract public static function rules(): array;
}
