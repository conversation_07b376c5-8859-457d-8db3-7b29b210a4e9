<?php

namespace App\Data;

use Closure;

abstract class FetchDataDTO
{
    /**
     * @var int
     */
    protected int $limit = 15;

    /**
     * @var int
     */
    protected int $page = 1;

    /**
     * @var null|string
     */
    protected ?string $keyword;

    /**
     * @var array
     */
    protected array $relations = [];

    /**
     * @var array
     */
    protected array $countRelations = [];

    /**
     * @var bool
     */
    protected bool $count = true;

    /**
     * @var array
     */
    protected array $orders = [];

    /**
     * @var Closure|null
     */
    protected ?Closure $queryCallback = null;

    /**
     * @var int
     */
    protected $maxLimit = 300;

    /**
     * @param array $data
     * @return void
     */
    public function __construct(array $data)
    {
        $this->setLimit($data['limit'] ?? 0);
        $this->setCurrentPage($data['page'] ?? 1);
        $this->setKeyword($data['keyword'] ?? null);
    }

    /**
     * @param array $data
     * @return static
     */
    public static function fromArray(array $data): static
    {
        return new static($data);
    }

    /**
     * @param array $relations
     * @param array $countRelations
     * @return $this
     */
    public function setRelations(array $relations = [], array $countRelations = [])
    {
        $this->relations = $relations;
        $this->countRelations = $countRelations;

        return $this;
    }

    /**
     * @return array
     */
    public function getRelations()
    {
        return $this->relations;
    }

    /**
     * @param int $limit
     * @return $this
     */
    public function setLimit(int $limit)
    {
        if ($limit) {
            if ($limit > $this->maxLimit) {
                $limit = $this->maxLimit;
            }

            $this->limit = $limit;
        }

        return $this;
    }

    /**
     * @return int
     */
    public function getLimit()
    {
        return $this->limit;
    }

    /**
     * @param int $page
     * @return $this
     */
    public function setCurrentPage(int $page)
    {
        if ($page < 1) {
            $page = 1;
        }

        $this->page = $page;

        return $this;
    }

    /**
     * @return int
     */
    public function currentPage()
    {
        return $this->page;
    }

    /**
     * @return array
     */
    public function getCountRelations()
    {
        return $this->countRelations;
    }

    /**
     * @return $this
     */
    public function withoutCount()
    {
        $this->count = false;

        return $this;
    }

    /**
     * @return bool
     */
    public function withCount()
    {
        return $this->count;
    }

    /**
     * @param array $orders
     * @return $this
     */
    public function setOrders(array $orders = [])
    {
        $this->orders = $orders;

        return $this;
    }

    /**
     * @return array
     */
    public function getOrders()
    {
        return $this->orders;
    }

    /**
     * @param string|null $keyword
     * @return $this
     */
    public function setKeyword(?string $keyword)
    {
        $this->keyword = $keyword;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getKeyword()
    {
        return $this->keyword;
    }

    /**
     * @param Closure|null $callback
     * @return void
     */
    public function setQueryCallback(?Closure $callback)
    {
        $this->queryCallback = $callback;
    }

    /**
     * @return Closure|null
     */
    public function getQueryCallback(?Closure $defaultCallback = null)
    {
        return $this->queryCallback ?? $defaultCallback;
    }
}
