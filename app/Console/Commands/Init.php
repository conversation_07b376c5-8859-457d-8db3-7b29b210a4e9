<?php

namespace App\Console\Commands;

use App\Events\MessageSent;
use App\Models\Notification;
use App\Models\User;
use App\Notifications\FcmPushNotification;
use Illuminate\Console\Command;

class Init extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:init';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Initialize the application.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        /** @var User $user */
        $user = User::query()->find(1);
        /*$notification = Notification::query()->create([
            'type' => 'test',
            'notifiable_type' => 'user',
            'notifiable_id' => 1,
            'data' => [],
            'status' => 1,
        ]);

        $user->notify(new FcmPushNotification($notification));*/

        MessageSent::dispatch('Hello World', 1);
    }
}
