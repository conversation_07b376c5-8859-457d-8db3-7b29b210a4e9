<?php

namespace App\Providers;

use App\Listeners\DeleteExpiredNotificationTokens;
use Illuminate\Notifications\Events\NotificationFailed;
use Illuminate\Support\ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        NotificationFailed::class => [
            DeleteExpiredNotificationTokens::class,
        ],
    ];

    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
