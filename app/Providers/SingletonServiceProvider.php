<?php

namespace App\Providers;

use App\Helpers\ChatWork;
use App\Repositories\Contracts\UserRepositoryInterface;
use App\Repositories\Eloquent\UserRepository;
use App\Services\ExceptionHandlerService;
use App\Services\JwtSanctumTokenService;
use App\Services\UserService;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Support\ServiceProvider;

class SingletonServiceProvider extends ServiceProvider
{
    /**
     * @return void
     */
    public function register()
    {
        $this->registerServices();
        $this->registerRepositories();
    }

    /**
     * @return void
     */
    protected function registerServices()
    {
        $services = [
            ChatWork::class,
            ExceptionHandlerService::class,
            JwtSanctumTokenService::class,
            UserService::class,
        ];

        foreach ($services as $service) {
            $this->app->singleton($service);
        }
    }

    /**
     * @return void
     */
    protected function registerRepositories()
    {
        $repositories = [
            UserRepositoryInterface::class => UserRepository::class,
        ];

        foreach ($repositories as $interface => $repository) {
            $this->app->singleton($interface, fn (Application $app) => new $repository(fn () => $app['request']));
        }
    }
}
