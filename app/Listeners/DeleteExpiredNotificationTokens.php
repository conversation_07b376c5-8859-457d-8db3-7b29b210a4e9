<?php

namespace App\Listeners;

use App\Models\Device;
use App\Models\User;
use Illuminate\Notifications\Events\NotificationFailed;
use Illuminate\Support\Arr;
use Kreait\Firebase\Messaging\SendReport;
use NotificationChannels\Fcm\FcmChannel;

class DeleteExpiredNotificationTokens
{
    /**
     * Handle the event.
     * @param NotificationFailed $event
     * @return void
     */
    public function handle(NotificationFailed $event): void
    {
        if ($event->channel == FcmChannel::class) {

            /** @var SendReport $report */
            $report = Arr::get($event->data, 'report');

            $target = $report->target();

            /** @var User $user */
            $user = $event->notifiable;

            /** @var Device $device */
            $device = $user->devices()->where('token', $target->value())->first();
            if (! is_null($device)) {
                if ($device->failed_count >= 3) {
                    $device->markAsDeleted();
                    return;
                }

                $device->incrementFailedCount();
            }
        }
    }
}
