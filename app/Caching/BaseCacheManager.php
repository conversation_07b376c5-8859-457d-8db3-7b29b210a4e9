<?php

namespace App\Caching;

use Illuminate\Contracts\Cache\Repository as CacheRepository;

abstract class BaseCacheManager
{
    /**
     * @var string
     */
    protected string $prefix;

    /**
     * @var int
     */
    protected int $ttl;

    /**
     * @param CacheRepository $cache
     * @return void
     */
    public function __construct(protected CacheRepository $cache)
    {
        $this->ttl = 3600;
    }

    /**
     * @param string $key
     * @return mixed
     * @throws \Psr\SimpleCache\InvalidArgumentException
     */
    public function get(string $key): mixed
    {
        return $this->cache->get("{$this->prefix}.{$key}");
    }

    /**
     * @param string $key
     * @param mixed $value
     * @param null|int $ttl
     * @return void
     */
    public function put(string $key, mixed $value, ?int $ttl = null): void
    {
        $this->cache->put("{$this->prefix}.{$key}", $value, $ttl ?? $this->ttl);
    }

    /**
     * @param string $key
     * @return void
     */
    public function forget(string $key): void
    {
        $this->cache->forget("{$this->prefix}.{$key}");
    }

    /**
     * @param string $key
     * @param callable $callback
     * @return mixed
     */
    public function remember(string $key, callable $callback): mixed
    {
        return $this->cache->remember("{$this->prefix}.{$key}", $this->ttl, $callback);
    }

    /**
     * @param string $key
     * @param callable $callback
     * @return mixed
     */
    public function rememberForever(string $key, callable $callback): mixed
    {
        return $this->cache->rememberForever("{$this->prefix}.{$key}", $callback);
    }
}
