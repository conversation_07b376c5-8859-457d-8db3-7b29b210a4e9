<?php

namespace App\Helpers;

use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Throwable;

/**
 * Class ChatWork
 * @package App\Helpers
 */
class ChatWork
{
    /**
     * @var Client|null
     */
    protected ?Client $client = null;

    /**
     * @return Client
     */
    protected function getClient(): Client
    {
        if (! $this->client) {
            $token = config('services.chatwork.api_token');
            $this->client = new Client([
                'base_uri' => 'https://api.chatwork.com/v2/',
                'headers' => [
                    'X-ChatWorkToken' => $token,
                ],
            ]);
        }

        return $this->client;
    }

    /**
     * @param array $payload
     */
    protected function report(array $payload): void
    {
        if ($chatWorkRoomId = config('services.chatwork.room_id')) {
            $client = $this->getClient();
            $time = now(config('app.timezone'))->toDateTimeString();

            try {
                $text = [];

                /**
                 * env
                 */
                $text[] = 'ENV: ' . config('app.env');

                /**
                 * error url
                 */
                if ($url = Arr::get($payload, 'url')) {
                    $text[] = 'URL: ' . $url;
                }

                /**
                 * error message
                 */
                $text[] = 'Message: ' . Arr::get($payload, 'message', 'Debug Data');

                /**
                 * validation errors
                 */
                if ($errors = Arr::get($payload, 'errors')) {
                    $text[] = 'Validation Errors: ' . json_encode($errors);
                }

                /**
                 * post data
                 */
                if ($data = Arr::get($payload, 'data')) {
                    $text[] = 'Request Data: ' . json_encode($data);
                }

                /**
                 * time
                 */
                $text[] = 'UTC Time: ' . $time;

                /**
                 * trace
                 */
                if ($trace = Arr::get($payload, 'trace')) {
                    $text[] = '======================================';
                    $text[] = 'Trace:';
                    $text[] = $trace;
                }

                $client->post("rooms/{$chatWorkRoomId}/messages", [
                    'form_params' => [
                        'body' => '[code]' . implode("\n", $text) . '[/code]',
                    ],
                ]);
            } catch (Throwable $e) {
                Log::error('Send message to Chat Work error:' . $e->getMessage(), $payload);
            }
        }
    }

    /**
     * @param Request $request
     * @param Throwable|null $throwable
     * @return void
     */
    public static function sendDebugReport(Request $request, ?Throwable $throwable = null)
    {
        if (! config('app.debug')) {
            return;
        }

        /** @var static $instance */
        $instance = app(ChatWork::class);
        $errors = ($isValidationException = $throwable instanceof ValidationException) ? $throwable->errors() : [];

        $message = 'Debug Report';
        $trace = null;
        if ($throwable && ! $isValidationException) {
            $message = $throwable->getMessage() ?: $message;
            $trace = $throwable->getTraceAsString();
        }

        $instance->report([
            'url' => $request->fullUrl(),
            'data' => [
                'get' => $request->query(),
                'post' => $request->post(),
                'files' => $request->allFiles(),
            ],
            'message' => $message,
            'trace' => $trace,
            'errors' => $errors,
        ]);
    }
}
