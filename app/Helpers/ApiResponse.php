<?php

namespace App\Helpers;

use Illuminate\Http\Request;

/**
 * Class ApiResponse
 * @package App\Helpers
 */
class ApiResponse
{
    /**
     * @param array $meta
     * @return array
     */
    protected static function parseMeta(array $meta)
    {
        /** trang thai bat user phai update app hay khong */
        $updateAppStatus = 0;

        /** @var Request $request */
        $request = request();

        $appver = $request->get('appver', 1);
        if (version_compare($appver, '1') < 0) {
            $updateAppStatus = 2;
        }

        $meta['updateApp'] = $updateAppStatus;

        return $meta;
    }

    /**
     * @param array $payload
     * @param array $meta
     * @return array
     */
    protected static function response(array $payload = [], array $meta = []): array
    {
        if (! isset($payload['errorCode'])) {
            $payload['errorCode'] = 0;
        }

        if (! isset($payload['errorMessage'])) {
            $payload['errorMessage'] = $payload['errorCode'] ? __('error.' . $payload['errorCode']) : __('success');
        }

        if (! isset($payload['data']) || isset($payload['errors'])) {
            $payload['data'] = null;
        }

        $payload['meta'] = static::parseMeta($meta);

        return $payload;
    }

    /**
     * Success response with metadata
     *
     * @param array|null $data
     * @param array $meta
     * @return array
     */
    public static function success(?array $data = null, array $meta = []): array
    {
        return static::response([
            'data' => $data,
        ], $meta);
    }

    /**
     * Error response with metadata
     *
     * @param string $message
     * @param int $code
     * @param array $errors
     * @param array $meta
     * @return array
     */
    public static function error(int $code, string $message, array $errors = [], array $meta = []): array
    {
        return static::response([
            'errorCode' => $code,
            'errorMessage' => $message,
            'errors' => $errors,
        ], $meta);
    }
}
