<?php

namespace App\Exceptions\Handlers;

use App\Helpers\ApiResponse;
use Illuminate\Http\Exceptions\ThrottleRequestsException;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Throwable;

class ThrottleExceptionHandler implements ExceptionHandlerInterface
{
    /**
     * @param Throwable $exception
     * @param Request $request
     * @return bool
     */
    public function shouldHandle(Throwable $exception, Request $request): bool
    {
        return $exception instanceof ThrottleRequestsException;
    }

    /**
     * @param Request $request
     * @return bool
     */
    public function isJsonOrApiRequest(Request $request): bool
    {
        if ($request->hasHeader('x-inertia')) {
            return false;
        }

        return $request->expectsJson() || $request->is('api/*');
    }

    /**
     * @param Throwable $exception
     * @param Request $request
     * @param int $responseCode
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function handle(Throwable $exception, Request $request, int $responseCode)
    {
        /** @var ThrottleRequestsException $exception */
        $headers = $exception->getHeaders();

        $throttleMessage = __('throttle', [
            'total' => Arr::get($headers, 'X-RateLimit-Limit', 5),
            'seconds' => Arr::get($headers, 'Retry-After', 0),
        ]);

        if ($this->isJsonOrApiRequest($request)) {
            return response()->json(ApiResponse::error(
                400,
                $throttleMessage,
            ));
        }

        return back()->with('flash', [
            'throttle' => $throttleMessage,
        ]);
    }
}
