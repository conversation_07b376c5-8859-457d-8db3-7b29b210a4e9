<?php

namespace App\Exceptions\Handlers;

use App\Helpers\ApiResponse;
use App\Helpers\ChatWork;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Throwable;

class ApiOrValidationExceptionHandler implements ExceptionHandlerInterface
{
    /**
     * @param Throwable $exception
     * @param Request $request
     * @return bool
     */
    public function shouldHandle(Throwable $exception, Request $request): bool
    {
        if ($request->hasHeader('x-inertia')) {
            return false;
        }

        return $request->expectsJson() || $request->is('api/*');
    }

    /**
     * @param Throwable $exception
     * @param Request $request
     * @param int $responseCode
     * @return \Illuminate\Http\JsonResponse
     */
    public function handle(Throwable $exception, Request $request, int $responseCode)
    {
        $errorCode = (int) (method_exists($exception, 'getStatusCode') ? $exception->getStatusCode() : $exception->getCode());
        $errorMessage = $exception->getMessage();

        $errors = [];
        if ($exception instanceof ValidationException) {
            $routeName = (string) $request->route()->getName();

            $errorCode = 2;
            $errors = collect($exception->errors())->map(fn ($messages) => $messages[0])->toArray();
            $errorMessage = __('validationException.' . $routeName);

            ChatWork::sendDebugReport($request, $exception);
        }

        return response()->json(ApiResponse::error(
            $errorCode,
            $errorMessage,
            $errors,
        ));
    }
}
