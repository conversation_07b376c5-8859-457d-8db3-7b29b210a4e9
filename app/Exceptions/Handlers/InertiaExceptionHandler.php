<?php

namespace App\Exceptions\Handlers;

use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use Throwable;

class InertiaExceptionHandler implements ExceptionHandlerInterface
{
    /**
     * @param Throwable $exception
     * @param Request $request
     * @return bool
     */
    public function shouldHandle(Throwable $exception, Request $request): bool
    {
        return $request->hasHeader('x-inertia') && ! $exception instanceof ValidationException;
    }

    /**
     * @param Throwable $exception
     * @param Request $request
     * @param int $responseCode
     * @return \Illuminate\Http\JsonResponse|\Symfony\Component\HttpFoundation\Response
     */
    public function handle(Throwable $exception, Request $request, int $responseCode)
    {
        return Inertia::render('Error', [
            'status' => $responseCode,
            'message' => $exception->getMessage(),
        ])
            ->toResponse($request)
            ->setStatusCode($responseCode);
    }
}
