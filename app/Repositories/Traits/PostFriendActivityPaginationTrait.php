<?php

namespace App\Repositories\Traits;

use App\Data\CursorPaginationDTO;
use App\Models\Post;
use Illuminate\Support\Facades\DB;

trait PostFriendActivityPaginationTrait
{
    use CursorPaginationTrait;

    /**
     * <PERSON><PERSON> trang bài post theo thời gian tương tác gần nhất của bạn bè (like, comment, đăng)
     *
     * @param CursorPaginationDTO $dto
     * @param array $friendIds
     * @return \App\Data\CursorPaginatedResultDTO
     */
    public function paginateByFriendActivity(CursorPaginationDTO $dto, array $friendIds)
    {
        $query = Post::query()
            ->leftJoin('comments', function($join) use ($friendIds) {
                $join->on('posts.id', '=', 'comments.post_id')
                     ->whereIn('comments.user_id', $friendIds);
            })
            ->leftJoin('post_likes', function($join) use ($friendIds) {
                $join->on('posts.id', '=', 'post_likes.post_id')
                     ->whereIn('post_likes.user_id', $friendIds);
            })
            ->select(
                'posts.*',
                DB::raw('GREATEST(
                    UNIX_TIMESTAMP(posts.created_at),
                    COALESCE(MAX(UNIX_TIMESTAMP(comments.created_at)), 0),
                    COALESCE(MAX(UNIX_TIMESTAMP(post_likes.created_at)), 0)
                ) as last_friend_activity')
            )
            ->groupBy('posts.id');

        return $this->cursorPaginateQuery(
            $query,
            $dto,
            null,
            'last_friend_activity'
        );
    }
}
