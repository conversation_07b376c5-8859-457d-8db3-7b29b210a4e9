<?php

namespace App\Repositories\Traits;

use App\Data\CursorPaginationDTO;
use App\Data\CursorPaginatedResultDTO;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

trait CursorPaginationTrait
{
    /**
     * Paginates a query using cursor-based pagination with multi-column sorting.
     *
     * @param Builder $query
     * @param CursorPaginationDTO $dto
     * @param callable|null $customizeQuery
     * @return CursorPaginatedResultDTO
     */
    public function cursorPaginateQuery(
        Builder $query,
        CursorPaginationDTO $dto,
        ?callable $customizeQuery = null
    ): CursorPaginatedResultDTO {
        $this->applyFiltersAndCustomizations($query, $dto, $customizeQuery);
        $this->applySorting($query, $dto);
        $this->applyCursor($query, $dto);

        // Fetch one more item than the limit to check if there are more pages
        $items = $query->limit($dto->limit + 1)->get();
        
        $hasMore = $items->count() > $dto->limit;
        $paginatedData = $items->take($dto->limit);

        $nextCursor = $this->createNextCursor($paginatedData, $dto->sorts, $hasMore);

        return new CursorPaginatedResultDTO($paginatedData->toArray(), $nextCursor, $hasMore);
    }

    /**
     * Applies filters, automatic scopes, and custom query modifications.
     */
    private function applyFiltersAndCustomizations(Builder $query, CursorPaginationDTO $dto, ?callable $customizeQuery): void
    {
        /**
         * apply filters
         */
        if ($this instanceof FilterableRepositoryInterface) {
            $this->applyQueryScope($query, $dto);
        }

        // Finally, apply any specific customizations from the caller
        if ($customizeQuery) {
            $customizeQuery($query, $dto);
        }
    }

    /**
     * Applies multi-column sorting to the query.
     */
    private function applySorting(Builder $query, CursorPaginationDTO $dto): void
    {
        foreach ($dto->sorts as $sort) {
            $query->orderBy($sort['column'], $sort['direction']);
        }
    }

    /**
     * Applies the cursor conditions to the query for pagination.
     */
    private function applyCursor(Builder $query, CursorPaginationDTO $dto): void
    {
        if (! $dto->cursor) {
            return;
        }

        $cursorValues = json_decode(base64_decode($dto->cursor), true);
        $sortColumns = array_column($dto->sorts, 'column');

        // This check ensures the cursor is valid for the current sort configuration
        if (! is_array($cursorValues) || count($cursorValues) !== count($sortColumns)) {
            // Or throw an exception, depending on the desired behavior for invalid cursors
            return;
        }

        $query->where(function (Builder $q) use ($dto, $cursorValues, $sortColumns) {
            $q->where(function (Builder $innerQuery) use ($dto, $cursorValues, $sortColumns) {
                foreach ($sortColumns as $index => $column) {
                    $innerQuery->orWhere(function (Builder $subQuery) use ($dto, $cursorValues, $sortColumns, $index, $column) {
                        for ($i = 0; $i < $index; $i++) {
                            $subQuery->where($sortColumns[$i], '=', $cursorValues[$i]);
                        }
                        $direction = strtolower($dto->sorts[$index]['direction']) === 'asc' ? '>' : '<';
                        $subQuery->where($column, $direction, $cursorValues[$index]);
                    });
                }
            });
        });
    }

    /**
     * Creates the next cursor string from the paginated data.
     */
    private function createNextCursor(Collection $paginatedData, array $sorts, bool $hasMore): ?string
    {
        if (! $hasMore || $paginatedData->isEmpty()) {
            return null;
        }

        $lastItem = $paginatedData->last();
        $sortColumns = array_column($sorts, 'column');
        
        $cursorData = [];
        foreach ($sortColumns as $column) {
            // This accesses the value from the Eloquent model/stdClass object.
            // It will correctly get aliased fields from `selectRaw` as well.
            $cursorData[] = $lastItem->{$column};
        }
        
        return base64_encode(json_encode($cursorData));
    }
}
