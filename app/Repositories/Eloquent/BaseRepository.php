<?php

namespace App\Repositories\Eloquent;

use App\Data\FetchDataDTO;
use App\Models\Contracts\HasStatusInterface;
use App\Repositories\Contracts\FilterableRepositoryInterface;
use Closure;
use Illuminate\Contracts\Database\Query\Expression;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Schema;

/**
 * Class BaseRepository
 * @package App\Repositories\Eloquent
 */
abstract class BaseRepository
{
    /**
     * @var Model
     */
    protected $model;

    /**
     * @var array
     */
    protected array $modelColumns = [];

    /**
     * @var string
     */
    protected string $modelTable = '';

    /**
     * @var string
     */
    protected string $primaryKey = '';

    /**
     * @var Request
     */
    protected Request $request;

    /**
     * @param $request
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function __construct($request)
    {
        $this->request = $request();
    }

    /**
     * @return Builder
     */
    abstract protected function query(): Builder;

    /**
     * @return Model
     */
    protected function getModel()
    {
        return $this->query()->getModel();
    }

    /**
     * @return string
     */
    public function getPrimaryKey(): string
    {
        if (! $this->primaryKey) {
            $this->primaryKey = $this->getModel()->getKeyName();
        }

        return $this->primaryKey;
    }

    /**
     * @return string
     */
    public function getTable(): string
    {
        if (! $this->modelTable) {
            $this->modelTable = $this->getModel()->getTable();
        }

        return $this->modelTable;
    }

    /**
     * @param $column
     * @return string
     */
    public function qualifyColumn($column)
    {
        return $this->getModel()->qualifyColumn($column);
    }

    /**
     * Qualify the given columns with the model's table.
     * @param $columns
     * @return array
     */
    public function qualifyColumns($columns)
    {
        return $this->getModel()->qualifyColumns($columns);
    }

    /**
     * @return array
     */
    protected function getColumns(): array
    {
        if (! $this->modelColumns) {
            $this->modelColumns = Schema::getColumnListing($this->getTable());
        }

        return $this->modelColumns;
    }

    /**
     * @param mixed $id
     * @param array|string $columns
     * @return \Illuminate\Database\Eloquent\Collection|Model|null
     */
    public function find(mixed $id, array|string $columns = ['*'])
    {
        return $this->query()->find($id, $columns);
    }

    /**
     * @param array $ids
     * @param array|string $columns
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function findMany(array $ids, array|string $columns = ['*'])
    {
        return $this->query()->findMany($ids, $columns);
    }

    /**
     * @param mixed $id
     * @param array|string $columns
     * @return \Illuminate\Database\Eloquent\Collection|Model|null
     */
    public function findOrFail(mixed $id, array|string $columns = ['*'])
    {
        return $this->query()->findOrFail($id, $columns);
    }

    /**
     * @param mixed $id
     * @param array|string $columns
     * @return \Illuminate\Database\Eloquent\Collection|Model|null
     */
    public function findOrNew(mixed $id, array|string $columns = ['*'])
    {
        return $this->query()->findOrNew($id, $columns);
    }

    /**
     * @param array $attributes
     * @param array $values
     * @return Model
     */
    public function firstOrNew(array $attributes = [], array $values = [])
    {
        return $this->query()->firstOrNew($attributes, $values);
    }

    /**
     * @param array $attributes
     * @param array $values
     * @return Model
     */
    public function firstOrCreate(array $attributes = [], array $values = [])
    {
        return $this->query()->firstOrCreate($attributes, $values);
    }

    /**
     * @param array $attributes
     * @param array $values
     * @return Model
     */
    public function updateOrCreate(array $attributes, array $values = [])
    {
        return $this->query()->updateOrCreate($attributes, $values);
    }

    /**
     * @param int $id
     * @param array $payload
     * @return \Illuminate\Database\Eloquent\Collection|Model|\Illuminate\Support\HigherOrderTapProxy|null
     */
    public function findOrCreate(int $id, array $payload)
    {
        return tap($this->findOrNew($id), function ($instance) use ($payload) {
            $instance->fill($payload)->save();
        });
    }

    /**
     * @param array $payload
     * @return Model
     */
    public function create(array $payload)
    {
        $model = $this->getModel();
        $model->fill($payload)->save();

        return $model;
    }

    /**
     * @param Model $model
     * @param array $payload
     * @return Model
     */
    public function update(Model $model, array $payload)
    {
        $model->update($payload);

        return $model;
    }

    /**
     * @param Model $model
     * @return void
     */
    public function destroy(Model $model)
    {
        if ($model instanceof HasStatusInterface) {
            $model->markAsDeleted();
        }
    }

    /**
     * @param string $column
     * @return bool
     */
    protected function isValidColumn(string $column): bool
    {
        return in_array($column, $this->getColumns());
    }

    /**
     * @param string $column
     * @return bool
     */
    protected function isInvalidColumn(string $column): bool
    {
        return ! $this->isValidColumn($column);
    }

    /**
     * @param string $direction
     * @return bool
     */
    protected function isValidSortDirection(string $direction): bool
    {
        $direction = strtolower($direction);

        return in_array($direction, ['asc', 'desc']);
    }

    /**
     * @param string $direction
     * @return bool
     */
    protected function isInvalidSortDirection(string $direction): bool
    {
        return ! $this->isValidSortDirection($direction);
    }

    /**
     * @param Builder $query
     * @param array $orders
     * @return void
     */
    public function applyQuerySorts(Builder $query, array $orders = []): void
    {
        foreach ($orders as $column => $direction) {
            if ($this->isInvalidColumn($column) || $this->isInvalidSortDirection($direction)) {
                continue;
            }

            $query->orderBy($this->qualifyColumn($column), $direction);
        }

        $primaryKey = $this->getPrimaryKey();
        if (! isset($orders[$primaryKey])) {
            $query->orderBy($this->qualifyColumn($primaryKey), 'DESC');
        }
    }

    /**
     * @param Builder $query
     * @param array $relations
     * @param array $countRelations
     * @return void
     */
    public function applyQueryRelations(Builder $query, array $relations = [], array $countRelations = [])
    {
        /**
         * relations
         */
        if ($relations) {
            $query->with($relations);
        }

        /**
         * count
         */
        if ($countRelations) {
            $query->withCount($countRelations);
        }
    }

    /**
     * @param FetchDataDTO $dto
     * @param Closure|null $callback
     * @return Builder
     */
    public function getQueryBuilder(FetchDataDTO $dto, ?Closure $callback = null)
    {
        $query = $this->query();

        /**
         * apply filters
         */
        if ($this instanceof FilterableRepositoryInterface) {
            $this->applyQueryFilter($query, $dto);
        }

        /**
         * apply query callback
         */
        $queryCallback = $dto->getQueryCallback($callback);
        if ($queryCallback instanceof Closure) {
            $queryCallback($query);
        }

        /**
         * apply sorts
         */
        $this->applyQuerySorts($query, $dto->getOrders());

        /**
         * apply relations
         */
        $this->applyQueryRelations($query, $dto->getRelations(), $dto->getCountRelations());

        return $query;
    }

    /**
     * @param FetchDataDTO $dto
     * @return LengthAwarePaginator
     */
    public function pagination(FetchDataDTO $dto)
    {
        return $this->getQueryBuilder($dto)
            ->paginate($dto->getLimit())
            ->withQueryString();
    }

    /**
     * @param FetchDataDTO $dto
     * @return \Illuminate\Contracts\Pagination\CursorPaginator
     */
    public function cursorPagination(FetchDataDTO $dto)
    {
        return $this->getQueryBuilder($dto)
            ->cursorPaginate($dto->getLimit())
            ->withQueryString();
    }
}
