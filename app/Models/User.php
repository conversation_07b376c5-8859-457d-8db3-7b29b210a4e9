<?php

namespace App\Models;

use App\Enums\UserRole;
use App\Models\Contracts\HasStatusInterface;
use App\Models\Traits\Scopes\HasStatusScope;
use Carbon\Carbon;
use Illuminate\Auth\MustVerifyEmail;
use Illuminate\Contracts\Auth\MustVerifyEmail as MustVerifyEmailContract;
use Illuminate\Database\Eloquent\Attributes\Scope;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\RoutesNotifications;
use Laravel\Sanctum\HasApiTokens;

/**
 * @class User
 * @property int $user_id
 * @property string $name
 * @property string $email
 * @property string $password
 * @property string $role
 * @property string $remember_token
 * @property Carbon|null $email_verified_at
 * @property \Illuminate\Database\Eloquent\Collection|Notification[] $notifications
 * @property \Illuminate\Database\Eloquent\Collection|Device[] $devices
 * @method static create(array $payload)
 */
class User extends Authenticatable implements MustVerifyEmailContract, HasStatusInterface
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, MustVerifyEmail;

    use HasApiTokens, HasStatusScope, RoutesNotifications;

    /**
     * @var string
     */
    protected $table = 'users';

    /**
     * @var string
     */
    protected $primaryKey = 'user_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'email_verified_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'created_at',
        'updated_at',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'role' => 'string',
        ];
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function devices()
    {
        return $this->hasMany(Device::class, 'user_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function notifications()
    {
        return $this->hasMany(Notification::class, 'user_id');
    }

    /**
     * Specifies the user's FCM tokens
     *
     * @return array
     */
    public function routeNotificationForFcm()
    {
        /** @var HasStatusInterface $query */
        $query = $this->devices();

        return $query->active()
            ->get()
            ->pluck('token')
            ->toArray();
    }

    /**
     * @param Builder $query
     * @param string|null $keyword
     * @return void
     */
    #[Scope]
    protected function search(Builder $query, ?string $keyword)
    {
        if ($keyword) {
            $query->where(function (Builder $q) use ($keyword) {
                $q->where('name', 'like', '%' . $keyword . '%')
                  ->orWhere('email', 'like', '%' . $keyword . '%');
            });
        }
    }

    /**
     * @return bool
     */
    public function isAdmin(): bool
    {
        return $this->role === UserRole::ADMIN->value;
    }

    /**
     * @return bool
     */
    public function isNotAdmin(): bool
    {
        return ! $this->isAdmin();
    }

    /**
     * @param $value
     * @return int
     */
    protected function parseInt($value)
    {
        return (int) $value;
    }

    /**
     * @param $id
     * @return bool
     */
    public function isID($id)
    {
        return $this->parseInt($id) === $this->parseInt($this->getKey());
    }
}
