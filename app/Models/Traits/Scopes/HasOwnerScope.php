<?php

namespace App\Models\Traits\Scopes;

use App\Models\Contracts\HasStatusInterface;
use App\Models\User;
use Illuminate\Database\Eloquent\Attributes\Scope;

/**
 * @mixin \Illuminate\Database\Eloquent\Model
 * @property int $user_id
 * @property User $createdBy
 */
trait HasOwnerScope
{
    /**
     * Boot the trait to merge fillable and casts attributes.
     * @return void
     */
    public static function bootHasStatusScope()
    {
        static::retrieved(function ($model) {
            $model->mergeTraitFillable();
            $model->mergeTraitCasts();
        });

        static::saving(function ($model) {
            $model->mergeTraitFillable();
            $model->mergeTraitCasts();
        });
    }

    /**
     * @return void
     */
    public function mergeTraitFillable()
    {
        $this->mergeFillable([
            'user_id',
        ]);
    }

    /**
     * @return void
     */
    public function mergeTraitCasts()
    {
        $this->mergeCasts([
            'user_id' => 'integer',
        ]);
    }

    /**
     * Scope a query to only include items created by a specific user.
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    #[Scope]
    protected function byUser($query, int $userId)
    {
        return $query->where($this->getTable() . '.user_id', $userId);
    }

    /**
     * Scope a query to only include items created by active users.
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    #[Scope]
    protected function hasActiveCreator($query)
    {
        return $query->whereHas('createdBy', function ($query) {
            /** @var HasStatusInterface|\Illuminate\Database\Eloquent\Builder $query */
            if ($query->getModel() instanceof HasStatusInterface) {
                $query->active();
            }
        });
    }

    /**
     * Scope a query to include the creator of the model.
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    #[Scope]
    protected function withCreatedBy($query)
    {
        return $query->with('createdBy');
    }

    /**
     * Get the user that created the model.
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Check if the model is created by a specific user.
     * @return bool
     */
    public function ownerBy(int $userId)
    {
        return $this->user_id === $userId;
    }

    /**
     * @param int $userId
     * @return bool
     */
    public function notOwnerBy(int $userId)
    {
        return ! $this->ownerBy($userId);
    }
}
