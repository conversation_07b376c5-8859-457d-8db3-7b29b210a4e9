<?php

namespace App\Models\Traits\Scopes;

use Illuminate\Database\Eloquent\Attributes\Scope;

/**
 * @mixin \Illuminate\Database\Eloquent\Model
 * @property int $status
 */
trait HasStatusScope
{
    /**
     * Boot the trait to merge fillable and casts attributes.
     * @return void
     */
    public static function bootHasStatusScope()
    {
        static::retrieved(function ($model) {
            $model->mergeTraitFillable();
            $model->mergeTraitCasts();
        });

        static::saving(function ($model) {
            $model->mergeTraitFillable();
            $model->mergeTraitCasts();
        });
    }

    /**
     * @return void
     */
    public function mergeTraitFillable()
    {
        $this->mergeFillable([
            'status',
        ]);
    }

    /**
     * @return void
     */
    public function mergeTraitCasts()
    {
        $this->mergeCasts([
            'status' => 'integer',
        ]);
    }

    /**
     * Scope a query to only include active items.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    #[Scope]
    protected function active($query)
    {
        return $query->where($this->getTable() . '.status', 1);
    }

    /**
     * Scope a query to only include inactive items.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    #[Scope]
    protected function inactive($query)
    {
        return $query->where($this->getTable() . '.status', 0);
    }

    /**
     * Check if the model is active.
     * @return bool
     */
    public function isActive()
    {
        return 1 === (int) $this->status;
    }

    /**
     * Check if the model is inactive.
     * @return bool
     */
    public function isInactive()
    {
        return ! $this->isActive();
    }

    /**
     * @return void
     */
    public function markAsDeleted()
    {
        $this->updateQuietly([
            'status' => 0,
        ]);
    }
}
