<?php

namespace App\Models\Contracts;

/**
 * @method \Illuminate\Database\Eloquent\Builder hasActiveCreator()
 * @method \Illuminate\Database\Eloquent\Builder withCreatedBy()
 * @method \Illuminate\Database\Eloquent\Builder byUser()
 */
interface HasOwnerInterface
{
    /**
     * @return bool
     */
    public function ownerBy(int $userId);

    /**
     * @return void
     */
    public function notOwnerBy(int $userId);
}
