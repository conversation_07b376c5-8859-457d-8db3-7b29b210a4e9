<?php

namespace App\Models;

use App\Models\Contracts\HasStatusInterface;
use App\Models\Traits\Scopes\HasStatusScope;
use Illuminate\Database\Eloquent\Model;

/**
 * @property string $id
 * @property integer $user_id
 * @property string $token
 * @property integer $failed_count
 * @property integer $status
 */
class Device extends Model implements HasStatusInterface
{
    use HasStatusScope;

    /**
     * The table associated with the model.
     * @var string
     */
    protected $table = 'devices';

    /**
     * The attributes that are mass assignable.
     * @var string[]
     */
    protected $fillable = [
        'user_id',
        'token',
        'failed_count',
        'status',
    ];

    /**
     * The attributes that should be cast to native types.
     * @var array
     */
    protected $casts = [
        'user_id' => 'integer',
        'token' => 'string',
        'failed_count' => 'integer',
        'status' => 'integer',
    ];

    /**
     * @return void
     */
    public function incrementFailedCount()
    {
        $this->incrementQuietly('failed_count');
    }
}
