<?php

namespace App\Models;

use App\Models\Contracts\HasStatusInterface;
use App\Models\Traits\Scopes\HasStatusScope;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * @property string $id
 * @property string $type
 * @property string $object_type
 * @property integer $object_id
 * @property array $data
 * @property Carbon|null $read_at
 * @property integer $status
 */
class Notification extends Model implements HasStatusInterface
{
    use HasStatusScope;

    /**
     * The table associated with the model.
     * @var string
     */
    protected $table = 'notifications';

    /**
     * The primary key associated with the table.
     * @var string
     */
    protected $primaryKey = 'notification_id';

    /**
     * The attributes that are mass assignable.
     * @var string[]
     */
    protected $fillable = [
        'user_id',
        'type',
        'object_type',
        'object_id',
        'data',
        'read_at',
        'status',
    ];

    /**
     * The attributes that should be cast to native types.
     * @var array
     */
    protected $casts = [
        'data' => 'array',
        'read_at' => 'datetime',
        'status' => 'integer',
    ];

    /**
     * Get the notifiable entity that the notification belongs to.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo<\App\Models\User, $this>
     */
    public function object()
    {
        return $this->morphTo();
    }

    /**
     * Mark the notification as read.
     *
     * @return void
     */
    public function markAsRead()
    {
        if (is_null($this->read_at)) {
            $this->fill(['read_at' => $this->freshTimestamp()])->save();
        }
    }

    /**
     * Mark the notification as unread.
     *
     * @return void
     */
    public function markAsUnread()
    {
        if (! is_null($this->read_at)) {
            $this->fill(['read_at' => null])->save();
        }
    }

    /**
     * Determine if a notification has been read.
     *
     * @return bool
     */
    public function read()
    {
        return $this->read_at !== null;
    }

    /**
     * Determine if a notification has not been read.
     *
     * @return bool
     */
    public function unread()
    {
        return $this->read_at === null;
    }
}
