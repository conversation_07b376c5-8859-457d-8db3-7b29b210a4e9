<?php

namespace App\Models\QueryFilters;

use App\Models\Contracts\HasStatusInterface;
use Closure;
use Illuminate\Database\Eloquent\Builder;

class ActiveStatus
{
    /**
     * @param Builder|HasStatusInterface $query
     * @param Closure $next
     * @return mixed
     */
    public function handle($query, Closure $next)
    {
        if ($query->getModel() instanceof HasStatusInterface) {
            $query->active();
        }

        return $next($query);
    }
}
