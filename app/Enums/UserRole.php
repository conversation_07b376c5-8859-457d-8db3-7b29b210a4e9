<?php

namespace App\Enums;

enum UserRole: string
{
    case ADMIN = 'admin';

    case NORMAL = 'normal';

    /**
     * @return array
     */
    public static function toArray(): array
    {
        return [
            self::ADMIN->value,
            self::NORMAL->value,
        ];
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return __('user.role.' . $this->value);
    }
}
