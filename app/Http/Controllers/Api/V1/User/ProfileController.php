<?php

namespace App\Http\Controllers\Api\V1\User;

use App\Http\Controllers\Api\V1\BaseController;
use App\Http\Resources\Api\V1\UserResource;
use Illuminate\Http\Request;

class ProfileController extends BaseController
{
    /**
     * @param Request $request
     * @return array
     */
    public function __invoke(Request $request)
    {
        return $this->successResponse([
            'user' => UserResource::make($request->user()),
        ]);
    }
}
