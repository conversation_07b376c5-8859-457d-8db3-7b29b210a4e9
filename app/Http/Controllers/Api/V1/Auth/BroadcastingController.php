<?php

namespace App\Http\Controllers\Api\V1\Auth;

use Ably\AblyRest;
use Ably\Exceptions\AblyException;
use App\Http\Controllers\Api\V1\BaseController;

class BroadcastingController extends BaseController
{
    /**
     * @throws AblyException
     */
    public function auth()
    {
        $rest = new AblyRest([
            'key' => config('broadcasting.connections.ably.key'),
        ]);

        $tokenDetails = $rest->auth->requestToken(
            ['clientId' => '*']
        );

        return response()->json($tokenDetails->toArray());
    }
}
