<?php

namespace App\Http\Controllers\Api\V1\Auth;

use App\Http\Controllers\Api\V1\BaseController;
use App\Models\User;
use Illuminate\Http\Request;
use Laravel\Sanctum\PersonalAccessToken;

class LogoutController extends BaseController
{
    /**
     * @param Request $request
     * @return array
     */
    public function __invoke(Request $request)
    {
        /** @var User $user */
        $user = $request->user();

        $currentToken = $user->currentAccessToken();
        if ($currentToken instanceof PersonalAccessToken) {
            $currentToken->delete();
        }

        return $this->successResponse();
    }
}
