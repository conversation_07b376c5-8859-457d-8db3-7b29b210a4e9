<?php

namespace App\Http\Controllers\Api\V1\Auth;

use App\Http\Controllers\Api\V1\BaseController;
use App\Http\Requests\Api\Auth\LoginRequest;
use App\Services\JwtSanctumTokenService;

class LoginController extends BaseController
{
    /**
     * @param JwtSanctumTokenService $service
     */
    public function __construct(protected JwtSanctumTokenService $service)
    {

    }

    /**
     * @param LoginRequest $request
     * @return array
     * @throws \Illuminate\Validation\ValidationException
     */
    public function __invoke(LoginRequest $request)
    {
        $request->authenticate();

        $user = $request->user();
        if ($user->isInactive()) {
            abort(401, __('Unauthorized'));
        }

        return $this->successResponse([
            'token' => $this->service->generateJwtFromSanctumToken($user->createToken('')->plainTextToken)
        ]);
    }
}
