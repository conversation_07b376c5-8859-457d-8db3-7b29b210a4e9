<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Helpers\ApiResponse;

abstract class BaseController extends Controller
{
    /**
     * API Version
     */
    protected const API_VERSION = 'v1';

    /**
     * Success response with version info
     */
    protected function successResponse($data = [], $meta = [])
    {
        $defaultMeta = [
            'apiVersion' => self::API_VERSION,
        ];

        $meta = array_merge($defaultMeta, $meta);

        return ApiResponse::success($data, $meta);
    }

    /**
     * Error response with version info
     */
    protected function errorResponse($code, $message, $errors = [], $meta = [])
    {
        $defaultMeta = [
            'apiVersion' => self::API_VERSION,
        ];

        $meta = array_merge($defaultMeta, $meta);

        return ApiResponse::error($code, $message, $errors, $meta);
    }
}
