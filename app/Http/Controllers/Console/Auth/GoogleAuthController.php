<?php

namespace App\Http\Controllers\Console\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Console\Auth\GoogleLoginRequest;
use Laravel\Socialite\Facades\Socialite;

class GoogleAuthController extends Controller
{
    /**
     * redirect to google login
     */
    public function redirect()
    {
        return Socialite::driver('google')->redirect();
    }

    /**
     * @param GoogleLoginRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function callback(GoogleLoginRequest $request)
    {
        $request->authenticate();

        return redirect()->route('dashboard');
    }
}
