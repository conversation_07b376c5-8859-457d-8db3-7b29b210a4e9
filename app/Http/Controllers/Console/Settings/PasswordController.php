<?php

namespace App\Http\Controllers\Console\Settings;

use App\Data\User\UpdateUserPasswordDTO;
use App\Http\Controllers\Controller;
use App\Http\Requests\Console\Settings\ChangeUserPasswordRequest;
use App\Models\User;
use App\Services\UserService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class PasswordController extends Controller
{
    /**
     * Show the user's password settings page.
     */
    public function edit(Request $request): Response
    {
        /** @var User $user */
        $user = $request->user();

        return Inertia::render('settings/Password', [
            'hasCurrentPassword' => ! empty($user->getAttribute('password')),
        ]);
    }

    /**
     * Update the user's password.
     */
    public function update(ChangeUserPasswordRequest $request, UserService $service): RedirectResponse
    {
        $service->updateUserPassword($request->user(), new UpdateUserPasswordDTO($request->only('password')));

        return back();
    }
}
