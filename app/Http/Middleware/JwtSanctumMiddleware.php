<?php

namespace App\Http\Middleware;

use App\Services\JwtSanctumTokenService;
use Closure;
use Illuminate\Http\Request;

class JwtSanctumMiddleware
{
    public function __construct(protected JwtSanctumTokenService $service)
    {

    }

    /**
     * @param Request $request
     * @param Closure $next
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $header = $request->header('Authorization');
        if (! $header || ! str_starts_with($header, 'Bearer ')) {
            $this->sendUnauthorizedResponse();
        }

        $jwt = substr($header, 7);
        $this->service->authenticateUserFromJwt($jwt);

        return $next($request);
    }

    /**
     * @return void
     */
    protected function sendUnauthorizedResponse()
    {
        abort(401, 'Unauthorized');
    }
}
