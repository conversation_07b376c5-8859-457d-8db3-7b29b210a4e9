<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ApiVersionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        // Add API version headers
        if ($response instanceof JsonResponse) {
            $version = $this->extractVersionFromPath($request->path());
            
            $response->headers->set('X-API-Version', $version);
            $response->headers->set('X-API-Deprecated', $this->isDeprecated($version) ? 'true' : 'false');
            
            if ($this->isDeprecated($version)) {
                $response->headers->set('X-API-Deprecation-Warning', 'This API version is deprecated. Please upgrade to v2.');
                $response->headers->set('X-API-Migration-Guide', 'https://api.example.com/docs/migration-guide');
            }
        }

        return $response;
    }

    /**
     * Extract version from request path
     */
    private function extractVersionFromPath(string $path): string
    {
        $segments = explode('/', $path);
        return $segments[1] ?? 'v1'; // Default to v1 if no version specified
    }

    /**
     * Check if version is deprecated
     */
    private function isDeprecated(string $version): bool
    {
        $deprecatedVersions = ['v1'];
        return in_array($version, $deprecatedVersions);
    }
} 