<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * @method \App\Models\User user()
 */
class BaseRequest extends FormRequest
{
    /**
     * Flag to indicate that validation data should be taken from the query string.
     *
     * @var bool
     */
    protected $fromQuery = false;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the data to be validated.
     *
     * If `$fromQuery` is true, data is sourced from the query string.
     * Otherwise, it's sourced from the request body and files.
     *
     * @return array<string, mixed>
     */
    public function validationData(): array
    {
        if ($this->fromQuery) {
            return $this->query();
        }

        return array_replace_recursive($this->post(), $this->allFiles());
    }
}
