<?php

namespace App\Http\Requests\Console\Settings;

use App\Http\Requests\BaseRequest;
use App\Models\User;
use Illuminate\Validation\Rule;

class ProfileUpdateRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     * @return array
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => [
                'required',
                'string',
                'lowercase',
                'email',
                'max:255',
                Rule::unique(User::class)->ignore($this->user()->getKey(), 'user_id'),
            ],
        ];
    }

    /**
     * @return array
     */
    public function attributes()
    {
        return [
            'name' => __('validation.custom.name'),
            'email' => __('validation.custom.email_address'),
        ];
    }
}
