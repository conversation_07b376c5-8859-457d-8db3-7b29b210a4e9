<?php

namespace App\Http\Requests\Console\Settings;

use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rules\Password;

class ChangeUserPasswordRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'password' => [
                'required',
                Password::defaults(),
                'confirmed',
            ],
            'password_confirmation' => [
                'required',
            ],
        ];

        /**
         * trường hợp user đã có mật khẩu, thì verify mật khẩu hiện tại
         */
        $user = $this->user();
        if (! empty($user->getAttribute('password'))) {
            $rules['current_password'] = [
                'required',
                'current_password',
            ];
        }

        return $rules;
    }

    /**
     * @return array
     */
    public function attributes()
    {
        return [
            'password' => __('validation.attributes.new_password'),
            'password_confirmation' => __('validation.attributes.new_password_confirmation'),
        ];
    }
}
