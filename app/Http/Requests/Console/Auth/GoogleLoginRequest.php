<?php

namespace App\Http\Requests\Console\Auth;

use App\Http\Requests\BaseRequest;
use Illuminate\Support\Facades\Auth;
use Laravel\Socialite\Facades\Socialite;

class GoogleLoginRequest extends BaseRequest
{
    /**
     * Attempt to authenticate the request's credentials.
     */
    public function authenticate(): void
    {
        $googleUser = Socialite::driver('google')->user();
        $user = userRepository()->findByEmail($googleUser->getEmail());

        abort_unless($user && $user->isAdmin(), 403, 'Unauthorized');

        Auth::login($user, true);
    }
}
