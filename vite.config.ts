import vue from '@vitejs/plugin-vue';
import laravel from 'laravel-vite-plugin';
import path from 'path';
import { resolve } from 'node:path';
import { defineConfig } from 'vite';
import tailwindcss from 'tailwindcss';
import autoprefixer from 'autoprefixer';

export default defineConfig({
    plugins: [
        laravel({
            input: ['resources/js/app.ts'],
            refresh: true,
        }),
        vue({
            template: {
                transformAssetUrls: {
                    base: null,
                    includeAbsolute: false,
                },
            },
        }),
    ],
    resolve: {
        alias: {
            '@': path.resolve(__dirname, './resources/js'),
            'ziggy-js': resolve(__dirname, 'vendor/tightenco/ziggy'),
        },
    },
    optimizeDeps: {
        include: [
            'vue',
            '@inertiajs/vue3',
            'pinia',
            '@vueuse/core',
            'vue-i18n',
            'vue-toast-notification',
        ],
        exclude: [
            'ably', // Large dependency that should be loaded on demand
        ],
    },
    css: {
        postcss: {
            plugins: [
                tailwindcss,
                autoprefixer,
            ],
        },
    },
    build: {
        chunkSizeWarningLimit: 1600, // Tăng limit thay vì split chunks
        sourcemap: false, // Disable sourcemaps in production for smaller builds
        minify: 'esbuild', // Use esbuild for faster builds
        rollupOptions: {
            output: {
                // Shorter file names for JavaScript chunks
                entryFileNames: 'js/[name]-[hash:8].js',
                chunkFileNames: 'js/[name]-[hash:8].js',
                // Shorter file names for CSS and other assets
                assetFileNames: (assetInfo) => {
                    const fileName = assetInfo.names?.[0] || 'asset';
                    const info = fileName.split('.');
                    const ext = info[info.length - 1];
                    if (/\.(css)$/.test(fileName)) {
                        return `css/[name]-[hash:8].${ext}`;
                    }
                    if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/i.test(fileName)) {
                        return `images/[name]-[hash:8].${ext}`;
                    }
                    return `assets/[name]-[hash:8].${ext}`;
                },
                // Conservative approach - only split truly independent large libraries
                manualChunks: {
                    // Only the largest, most independent libraries
                    'primevue': ['primevue'],
                    'lodash': ['lodash'],
                    'moment': ['moment'],
                    'ably': ['ably'],
                },
            },
        },
    },
});
