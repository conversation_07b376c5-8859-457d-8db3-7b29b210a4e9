import vue from '@vitejs/plugin-vue';
import laravel from 'laravel-vite-plugin';
import path from 'path';
import { resolve } from 'node:path';
import { defineConfig } from 'vite';
import tailwindcss from 'tailwindcss';
import autoprefixer from 'autoprefixer';

export default defineConfig({
    plugins: [
        laravel({
            input: ['resources/js/app.ts'],
            refresh: true,
        }),
        vue({
            template: {
                transformAssetUrls: {
                    base: null,
                    includeAbsolute: false,
                },
            },
        }),
    ],
    resolve: {
        alias: {
            '@': path.resolve(__dirname, './resources/js'),
            'ziggy-js': resolve(__dirname, 'vendor/tightenco/ziggy'),
        },
    },
    optimizeDeps: {
        include: [
            'vue',
            '@inertiajs/vue3',
            'pinia',
            '@vueuse/core',
            'vue-i18n',
            'vue-toast-notification',
        ],
        exclude: [
            'ably', // Large dependency that should be loaded on demand
        ],
    },
    css: {
        postcss: {
            plugins: [
                tailwindcss,
                autoprefixer,
            ],
        },
    },
    build: {
        chunkSizeWarningLimit: 1000,
        sourcemap: false, // Disable sourcemaps in production for smaller builds
        minify: 'esbuild', // Use esbuild for faster builds
        rollupOptions: {
            output: {
                // Shorter file names for JavaScript chunks
                entryFileNames: 'js/[name]-[hash:8].js',
                chunkFileNames: 'js/[name]-[hash:8].js',
                // Shorter file names for CSS and other assets
                assetFileNames: (assetInfo) => {
                    const info = assetInfo.name.split('.');
                    const ext = info[info.length - 1];
                    if (/\.(css)$/.test(assetInfo.name)) {
                        return `css/[name]-[hash:8].${ext}`;
                    }
                    if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/i.test(assetInfo.name)) {
                        return `images/[name]-[hash:8].${ext}`;
                    }
                    return `assets/[name]-[hash:8].${ext}`;
                },
                manualChunks: (id) => {
                    // Core Vue ecosystem
                    if (id.includes('vue') || id.includes('@inertiajs')) {
                        return 'vue-vendor';
                    }

                    // UI Libraries
                    if (id.includes('primevue') || id.includes('reka-ui')) {
                        return 'ui-vendor';
                    }

                    // Icons
                    if (id.includes('lucide')) {
                        return 'icons-vendor';
                    }

                    // Utilities
                    if (id.includes('lodash') || id.includes('moment') ||
                        id.includes('clsx') || id.includes('tailwind-merge') ||
                        id.includes('class-variance-authority')) {
                        return 'utils-vendor';
                    }

                    // Communication/Real-time
                    if (id.includes('ably') || id.includes('echo')) {
                        return 'communication-vendor';
                    }

                    // Internationalization
                    if (id.includes('vue-i18n')) {
                        return 'i18n-vendor';
                    }

                    // State management
                    if (id.includes('pinia') || id.includes('@vueuse')) {
                        return 'state-vendor';
                    }

                    // Notifications
                    if (id.includes('toast') || id.includes('izitoast')) {
                        return 'toast-vendor';
                    }

                    // Other node_modules
                    if (id.includes('node_modules')) {
                        return 'vendor';
                    }
                },
            },
        },
    },
});
