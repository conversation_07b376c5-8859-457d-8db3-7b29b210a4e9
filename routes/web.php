<?php

use Illuminate\Support\Facades\Route;

/**
 * broadcast routes
 */
Route::group([], function () {
    require base_path('routes/channels.php');
});

/**
 * console routes
 */
$consoleDomain = config('app.console_domain');
if ($consoleDomain) {
    Route::domain($consoleDomain)->group(
        base_path('routes/console.php')
    );
} else {
    Route::prefix('console')->group(
        base_path('routes/console.php')
    );
}

/**
 * frontend routes
 */
Route::group([], function () {
    require base_path('routes/frontend.php');
});
