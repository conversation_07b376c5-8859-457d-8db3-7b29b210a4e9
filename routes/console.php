<?php

use App\Http\Controllers\Console\Auth;
use App\Http\Controllers\Console\Settings;
use App\Http\Controllers\Console\User;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

/**
 * guest routes
 */
Route::middleware('guest')->group(function () {
    Route::get('register', [Auth\RegisteredUserController::class, 'create'])
        ->name('register');

    Route::post('register', [Auth\RegisteredUserController::class, 'store']);

    Route::get('login', [Auth\AuthenticatedSessionController::class, 'create'])
        ->name('login');

    Route::post('login', [Auth\AuthenticatedSessionController::class, 'store']);

    Route::get('forgot-password', [Auth\PasswordResetLinkController::class, 'create'])
        ->name('password.request');

    Route::post('forgot-password', [Auth\PasswordResetLinkController::class, 'store'])
        ->name('password.email');

    Route::get('reset-password/{token}', [Auth\NewPasswordController::class, 'create'])
        ->name('password.reset');

    Route::post('reset-password', [Auth\NewPasswordController::class, 'store'])
        ->name('password.store');

    /**
     * google auth
     */
    Route::get('/auth/redirect', [Auth\GoogleAuthController::class, 'redirect'])->name('google.redirect');
    Route::get('/auth/callback', [Auth\GoogleAuthController::class, 'callback']);
});

/**
 * authenticated routes
 */
Route::middleware(['auth:sanctum', 'console'])->group(function () {
    Route::get('verify-email', Auth\EmailVerificationPromptController::class)
        ->name('verification.notice');

    Route::get('verify-email/{id}/{hash}', Auth\VerifyEmailController::class)
        ->middleware(['signed', 'throttle:60,1'])
        ->name('verification.verify');

    Route::post('email/verification-notification', [Auth\EmailVerificationNotificationController::class, 'store'])
        ->middleware('throttle:6,1')
        ->name('verification.send');

    /**
     * user verified email
     */
    Route::middleware('verified')->group(function () {
        /**
         * user routes
         */
        Route::get('/', User\IndexController::class)->name('dashboard');

        /**
         * other routes
         */
        Route::get('confirm-password', [Auth\ConfirmablePasswordController::class, 'show'])
            ->name('password.confirm');

        Route::post('confirm-password', [Auth\ConfirmablePasswordController::class, 'store']);

        Route::post('logout', [Auth\AuthenticatedSessionController::class, 'destroy'])
            ->name('logout');

        /**
         * settings routes
         */
        Route::redirect('settings', '/settings/profile');

        Route::get('settings/profile', [Settings\ProfileController::class, 'edit'])->name('profile.edit');
        Route::patch('settings/profile', [Settings\ProfileController::class, 'update'])->name('profile.update');
        Route::delete('settings/profile', [Settings\ProfileController::class, 'destroy'])->name('profile.destroy');

        Route::get('settings/password', [Settings\PasswordController::class, 'edit'])->name('password.edit');
        Route::put('settings/password', [Settings\PasswordController::class, 'update'])->name('password.update');

        Route::get('settings/appearance', function () {
            return Inertia::render('settings/Appearance');
        })->name('appearance');
    });
});

Route::get('/{slug}', fn () => Inertia::render('Error', [
    'message' => __('pageNotFound')
]))->where('slug', '.*');
