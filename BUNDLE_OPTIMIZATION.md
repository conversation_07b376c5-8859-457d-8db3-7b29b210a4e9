# Bundle Size Optimization Guide

## What was implemented

### 1. Manual Chunk Splitting
The Vite configuration now splits your bundle into logical chunks:
- `vue-vendor`: Vue.js and Inertia.js core
- `ui-vendor`: PrimeVue and Reka UI components
- `icons-vendor`: Lucide icons
- `utils-vendor`: Utility libraries (Lodash, Moment, etc.)
- `communication-vendor`: Ably and Echo for real-time features
- `i18n-vendor`: Vue i18n for internationalization
- `state-vendor`: Pinia and VueUse
- `toast-vendor`: Toast notification libraries
- `vendor`: Other third-party packages

### 2. Build Optimizations
- Increased chunk size warning limit to 1000KB
- Disabled sourcemaps in production
- Using esbuild for faster minification
- Optimized dependency pre-bundling

## Additional Recommendations

### 1. Replace Heavy Dependencies

#### Replace Moment.js with Day.js
Moment.js is large (~67KB). Replace with Day.js (~2KB):

```bash
npm uninstall moment
npm install dayjs
```

#### Optimize Lodash Usage
Instead of importing the entire Lodash library, use specific functions:

```javascript
// Instead of
import _ from 'lodash'

// Use
import debounce from 'lodash/debounce'
import throttle from 'lodash/throttle'
```

### 2. Lazy Load Heavy Components

#### Lazy Load PrimeVue Components
Only import PrimeVue components when needed:

```javascript
// In your components
import { defineAsyncComponent } from 'vue'

const DataTable = defineAsyncComponent(() => import('primevue/datatable'))
const Calendar = defineAsyncComponent(() => import('primevue/calendar'))
```

#### Lazy Load Pages
Your pages are already lazy-loaded via `import.meta.glob`, which is great!

### 3. Optimize Icon Usage

#### Use Lucide Tree Shaking
Instead of importing all icons, import only what you need:

```javascript
// Instead of
import * as icons from 'lucide-vue-next'

// Use
import { User, Settings, Home } from 'lucide-vue-next'
```

### 4. Code Splitting Strategies

#### Route-based Splitting
Consider splitting by feature/route:

```javascript
// In your router or component loader
const UserManagement = () => import('./pages/user/Index.vue')
const Settings = () => import('./pages/settings/Profile.vue')
```

#### Component-based Splitting
Split large components:

```javascript
const HeavyChart = defineAsyncComponent(() => import('./components/HeavyChart.vue'))
```

### 5. Bundle Analysis

Run bundle analysis to identify large dependencies:

```bash
npm install --save-dev rollup-plugin-visualizer
```

Add to vite.config.ts:
```javascript
import { visualizer } from 'rollup-plugin-visualizer'

export default defineConfig({
  plugins: [
    // ... other plugins
    visualizer({
      filename: 'dist/stats.html',
      open: true,
      gzipSize: true,
    })
  ]
})
```

### 6. Performance Monitoring

Monitor your bundle sizes:
- Main bundle should be < 250KB gzipped
- Each vendor chunk should be < 500KB gzipped
- Total initial load should be < 1MB gzipped

### 7. Progressive Loading

Consider implementing:
- Service Worker for caching
- Preloading critical routes
- Prefetching likely-to-be-visited pages

## Testing the Optimizations

1. Run `npm run build` to see the new chunk sizes
2. Check the build output for warnings
3. Test loading performance in development and production
4. Use browser dev tools to analyze network tab

## Expected Results

After these optimizations, you should see:
- Smaller initial bundle size
- Faster page load times
- Better caching efficiency
- Reduced bundle size warnings
